{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i1-d3-repotu/src/components/ui/Container.js"], "sourcesContent": ["'use client';\n\nimport { forwardRef } from 'react';\nimport { clsx } from 'clsx';\n\nconst Container = forwardRef(({ \n  children, \n  className, \n  size = 'default',\n  center = true,\n  ...props \n}, ref) => {\n  const baseClasses = 'w-full px-4 sm:px-6 lg:px-8';\n  \n  const sizes = {\n    sm: 'max-w-3xl',\n    default: 'max-w-7xl',\n    lg: 'max-w-screen-2xl',\n    full: 'max-w-none',\n  };\n  \n  const centerClass = center ? 'mx-auto' : '';\n  \n  const classes = clsx(\n    baseClasses,\n    sizes[size],\n    centerClass,\n    className\n  );\n  \n  return (\n    <div\n      ref={ref}\n      className={classes}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n});\n\nContainer.displayName = 'Container';\n\nexport default Container;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OAAE,CAAC,EAC5B,QAAQ,EACR,SAAS,EACT,OAAO,SAAS,EAChB,SAAS,IAAI,EACb,GAAG,OACJ,EAAE;IACD,MAAM,cAAc;IAEpB,MAAM,QAAQ;QACZ,IAAI;QACJ,SAAS;QACT,IAAI;QACJ,MAAM;IACR;IAEA,MAAM,cAAc,SAAS,YAAY;IAEzC,MAAM,UAAU,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACjB,aACA,KAAK,CAAC,KAAK,EACX,aACA;IAGF,qBACE,6LAAC;QACC,KAAK;QACL,WAAW;QACV,GAAG,KAAK;kBAER;;;;;;AAGP;;AAEA,UAAU,WAAW,GAAG;uCAET", "debugId": null}}, {"offset": {"line": 53, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i1-d3-repotu/src/components/ui/Button.js"], "sourcesContent": ["'use client';\n\nimport { forwardRef } from 'react';\nimport { clsx } from 'clsx';\n\nconst Button = forwardRef(({ \n  children, \n  className, \n  variant = 'primary', \n  size = 'md', \n  disabled = false,\n  loading = false,\n  onClick,\n  type = 'button',\n  ...props \n}, ref) => {\n  const baseClasses = 'inline-flex items-center justify-center font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed btn-hover';\n  \n  const variants = {\n    primary: 'bg-primary-blue hover:bg-primary-cyan text-white focus:ring-primary-cyan glow-hover',\n    secondary: 'bg-transparent border-2 border-primary-cyan text-primary-cyan hover:bg-primary-cyan hover:text-neutral-black focus:ring-primary-cyan',\n    accent: 'bg-accent-neon hover:bg-accent-gold text-neutral-black focus:ring-accent-neon',\n    ghost: 'bg-transparent text-primary-cyan hover:bg-primary-cyan/10 focus:ring-primary-cyan',\n    danger: 'bg-secondary-pink hover:bg-secondary-orange text-white focus:ring-secondary-pink',\n  };\n  \n  const sizes = {\n    sm: 'px-3 py-1.5 text-sm rounded-md',\n    md: 'px-4 py-2 text-base rounded-lg',\n    lg: 'px-6 py-3 text-lg rounded-xl',\n    xl: 'px-8 py-4 text-xl rounded-2xl',\n  };\n  \n  const classes = clsx(\n    baseClasses,\n    variants[variant],\n    sizes[size],\n    className\n  );\n  \n  return (\n    <button\n      ref={ref}\n      type={type}\n      className={classes}\n      disabled={disabled || loading}\n      onClick={onClick}\n      {...props}\n    >\n      {loading && (\n        <div className=\"w-4 h-4 mr-2 spinner\"></div>\n      )}\n      {children}\n    </button>\n  );\n});\n\nButton.displayName = 'Button';\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OAAE,CAAC,EACzB,QAAQ,EACR,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,WAAW,KAAK,EAChB,UAAU,KAAK,EACf,OAAO,EACP,OAAO,QAAQ,EACf,GAAG,OACJ,EAAE;IACD,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,UAAU,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACjB,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;IAGF,qBACE,6LAAC;QACC,KAAK;QACL,MAAM;QACN,WAAW;QACX,UAAU,YAAY;QACtB,SAAS;QACR,GAAG,KAAK;;YAER,yBACC,6LAAC;gBAAI,WAAU;;;;;;YAEhB;;;;;;;AAGP;;AAEA,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i1-d3-repotu/src/components/layout/Header.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';\nimport Container from '@/components/ui/Container';\nimport Button from '@/components/ui/Button';\nimport { clsx } from 'clsx';\n\nconst Header = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navigation = [\n    { name: 'Home', href: '/' },\n    { name: 'Demo', href: '/demo' },\n    { name: 'Why Us', href: '/why-us' },\n    { name: 'Pitch Deck', href: '/pitch-deck' },\n    { name: 'Roadmap', href: '/roadmap' },\n  ];\n\n  const headerClasses = clsx(\n    'fixed top-0 left-0 right-0 z-50 transition-all duration-300',\n    isScrolled \n      ? 'bg-neutral-black/90 backdrop-blur-md border-b border-neutral-gray/20 shadow-lg' \n      : 'bg-transparent'\n  );\n\n  return (\n    <header className={headerClasses}>\n      <Container>\n        <nav className=\"flex items-center justify-between py-4\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-3 group\">\n            <div className=\"w-10 h-10 bg-gradient-to-br from-primary-cyan to-primary-blue rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300\">\n              <svg\n                className=\"w-6 h-6 text-white\"\n                fill=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path d=\"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5\" />\n              </svg>\n            </div>\n            <span className=\"text-xl font-heading font-bold text-white group-hover:text-primary-cyan transition-colors duration-300\">\n              ReportU\n            </span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"text-neutral-white hover:text-primary-cyan transition-colors duration-300 font-medium relative group\"\n              >\n                {item.name}\n                <span className=\"absolute -bottom-1 left-0 w-0 h-0.5 bg-primary-cyan transition-all duration-300 group-hover:w-full\"></span>\n              </Link>\n            ))}\n          </div>\n\n          {/* CTA Buttons */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <Button variant=\"ghost\" size=\"sm\">\n              Sign In\n            </Button>\n            <Button variant=\"primary\" size=\"sm\" className=\"glow\">\n              Get Started\n            </Button>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            className=\"md:hidden p-2 text-white hover:text-primary-cyan transition-colors duration-300\"\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n            aria-label=\"Toggle menu\"\n          >\n            {isMenuOpen ? (\n              <XMarkIcon className=\"w-6 h-6\" />\n            ) : (\n              <Bars3Icon className=\"w-6 h-6\" />\n            )}\n          </button>\n        </nav>\n\n        {/* Mobile Navigation */}\n        <div\n          className={clsx(\n            'md:hidden overflow-hidden transition-all duration-300 ease-in-out',\n            isMenuOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'\n          )}\n        >\n          <div className=\"py-4 space-y-4 border-t border-neutral-gray/20\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"block text-neutral-white hover:text-primary-cyan transition-colors duration-300 font-medium py-2\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {item.name}\n              </Link>\n            ))}\n            <div className=\"pt-4 space-y-3\">\n              <Button variant=\"ghost\" size=\"sm\" className=\"w-full\">\n                Sign In\n              </Button>\n              <Button variant=\"primary\" size=\"sm\" className=\"w-full glow\">\n                Get Started\n              </Button>\n            </div>\n          </div>\n        </div>\n      </Container>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;;;AAPA;;;;;;;AASA,MAAM,SAAS;;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;iDAAe;oBACnB,cAAc,OAAO,OAAO,GAAG;gBACjC;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,MAAM,aAAa;QACjB;YAAE,MAAM;YAAQ,MAAM;QAAI;QAC1B;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAU,MAAM;QAAU;QAClC;YAAE,MAAM;YAAc,MAAM;QAAc;QAC1C;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IAED,MAAM,gBAAgB,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACvB,+DACA,aACI,mFACA;IAGN,qBACE,6LAAC;QAAO,WAAW;kBACjB,cAAA,6LAAC,uIAAA,CAAA,UAAS;;8BACR,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,WAAU;wCACV,MAAK;wCACL,SAAQ;kDAER,cAAA,6LAAC;4CAAK,GAAE;;;;;;;;;;;;;;;;8CAGZ,6LAAC;oCAAK,WAAU;8CAAyG;;;;;;;;;;;;sCAM3H,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;;wCAET,KAAK,IAAI;sDACV,6LAAC;4CAAK,WAAU;;;;;;;mCALX,KAAK,IAAI;;;;;;;;;;sCAWpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAQ,MAAK;8CAAK;;;;;;8CAGlC,6LAAC,oIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,WAAU;8CAAO;;;;;;;;;;;;sCAMvD,6LAAC;4BACC,WAAU;4BACV,SAAS,IAAM,cAAc,CAAC;4BAC9B,cAAW;sCAEV,2BACC,6LAAC,oNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;qDAErB,6LAAC,oNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAM3B,6LAAC;oBACC,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACZ,qEACA,aAAa,yBAAyB;8BAGxC,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,cAAc;8CAE5B,KAAK,IAAI;mCALL,KAAK,IAAI;;;;;0CAQlB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,UAAM;wCAAC,SAAQ;wCAAQ,MAAK;wCAAK,WAAU;kDAAS;;;;;;kDAGrD,6LAAC,oIAAA,CAAA,UAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;kDAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS1E;GArHM;KAAA;uCAuHS", "debugId": null}}, {"offset": {"line": 387, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i1-d3-repotu/src/components/ui/Typography.js"], "sourcesContent": ["'use client';\n\nimport { forwardRef } from 'react';\nimport { clsx } from 'clsx';\n\nconst Heading = forwardRef(({ \n  children, \n  className, \n  level = 1,\n  variant = 'default',\n  gradient = false,\n  neon = false,\n  typewriter = false,\n  ...props \n}, ref) => {\n  const Tag = `h${level}`;\n  \n  const baseClasses = 'font-heading font-bold leading-tight tracking-tight';\n  \n  const sizes = {\n    1: 'text-4xl sm:text-5xl lg:text-6xl xl:text-7xl',\n    2: 'text-3xl sm:text-4xl lg:text-5xl xl:text-6xl',\n    3: 'text-2xl sm:text-3xl lg:text-4xl xl:text-5xl',\n    4: 'text-xl sm:text-2xl lg:text-3xl xl:text-4xl',\n    5: 'text-lg sm:text-xl lg:text-2xl xl:text-3xl',\n    6: 'text-base sm:text-lg lg:text-xl xl:text-2xl',\n  };\n  \n  const variants = {\n    default: 'text-neutral-white',\n    primary: 'text-primary-cyan',\n    accent: 'text-accent-neon',\n    gradient: 'bg-gradient-to-r from-primary-cyan via-primary-blue to-primary-purple bg-clip-text text-transparent',\n  };\n  \n  const effects = {\n    neon: neon ? 'neon-text' : '',\n    typewriter: typewriter ? 'typewriter' : '',\n    gradient: gradient ? variants.gradient : variants[variant],\n  };\n  \n  const classes = clsx(\n    baseClasses,\n    sizes[level],\n    gradient ? effects.gradient : variants[variant],\n    effects.neon,\n    effects.typewriter,\n    className\n  );\n  \n  return (\n    <Tag\n      ref={ref}\n      className={classes}\n      {...props}\n    >\n      {children}\n    </Tag>\n  );\n});\n\nHeading.displayName = 'Heading';\n\nconst Text = forwardRef(({ \n  children, \n  className, \n  size = 'base',\n  variant = 'default',\n  weight = 'normal',\n  ...props \n}, ref) => {\n  const baseClasses = 'font-body leading-relaxed';\n  \n  const sizes = {\n    xs: 'text-xs',\n    sm: 'text-sm',\n    base: 'text-base',\n    lg: 'text-lg',\n    xl: 'text-xl',\n    '2xl': 'text-2xl',\n  };\n  \n  const variants = {\n    default: 'text-neutral-white',\n    muted: 'text-neutral-gray',\n    primary: 'text-primary-cyan',\n    accent: 'text-accent-neon',\n    success: 'text-secondary-green',\n    warning: 'text-secondary-orange',\n    danger: 'text-secondary-pink',\n  };\n  \n  const weights = {\n    light: 'font-light',\n    normal: 'font-normal',\n    medium: 'font-medium',\n    semibold: 'font-semibold',\n    bold: 'font-bold',\n  };\n  \n  const classes = clsx(\n    baseClasses,\n    sizes[size],\n    variants[variant],\n    weights[weight],\n    className\n  );\n  \n  return (\n    <p\n      ref={ref}\n      className={classes}\n      {...props}\n    >\n      {children}\n    </p>\n  );\n});\n\nText.displayName = 'Text';\n\nconst Code = forwardRef(({ \n  children, \n  className, \n  inline = true,\n  ...props \n}, ref) => {\n  const baseClasses = 'font-mono bg-neutral-dark/50 border border-neutral-gray/30 text-accent-neon';\n  \n  const inlineClasses = inline \n    ? 'px-1.5 py-0.5 rounded text-sm' \n    : 'block p-4 rounded-lg text-sm overflow-x-auto';\n  \n  const classes = clsx(\n    baseClasses,\n    inlineClasses,\n    className\n  );\n  \n  const Tag = inline ? 'code' : 'pre';\n  \n  return (\n    <Tag\n      ref={ref}\n      className={classes}\n      {...props}\n    >\n      {inline ? children : <code>{children}</code>}\n    </Tag>\n  );\n});\n\nCode.displayName = 'Code';\n\nconst Link = forwardRef(({ \n  children, \n  className, \n  variant = 'default',\n  external = false,\n  ...props \n}, ref) => {\n  const baseClasses = 'transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2';\n  \n  const variants = {\n    default: 'text-primary-cyan hover:text-primary-blue underline-offset-4 hover:underline focus:ring-primary-cyan',\n    button: 'text-neutral-white hover:text-primary-cyan no-underline focus:ring-primary-cyan',\n    muted: 'text-neutral-gray hover:text-neutral-white underline-offset-4 hover:underline focus:ring-neutral-gray',\n  };\n  \n  const classes = clsx(\n    baseClasses,\n    variants[variant],\n    className\n  );\n  \n  const externalProps = external ? {\n    target: '_blank',\n    rel: 'noopener noreferrer'\n  } : {};\n  \n  return (\n    <a\n      ref={ref}\n      className={classes}\n      {...externalProps}\n      {...props}\n    >\n      {children}\n      {external && (\n        <span className=\"ml-1 text-xs\">↗</span>\n      )}\n    </a>\n  );\n});\n\nLink.displayName = 'Link';\n\nexport { Heading, Text, Code, Link };\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,wBAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OAAE,CAAC,EAC1B,QAAQ,EACR,SAAS,EACT,QAAQ,CAAC,EACT,UAAU,SAAS,EACnB,WAAW,KAAK,EAChB,OAAO,KAAK,EACZ,aAAa,KAAK,EAClB,GAAG,OACJ,EAAE;IACD,MAAM,MAAM,CAAC,CAAC,EAAE,OAAO;IAEvB,MAAM,cAAc;IAEpB,MAAM,QAAQ;QACZ,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;IACL;IAEA,MAAM,WAAW;QACf,SAAS;QACT,SAAS;QACT,QAAQ;QACR,UAAU;IACZ;IAEA,MAAM,UAAU;QACd,MAAM,OAAO,cAAc;QAC3B,YAAY,aAAa,eAAe;QACxC,UAAU,WAAW,SAAS,QAAQ,GAAG,QAAQ,CAAC,QAAQ;IAC5D;IAEA,MAAM,UAAU,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACjB,aACA,KAAK,CAAC,MAAM,EACZ,WAAW,QAAQ,QAAQ,GAAG,QAAQ,CAAC,QAAQ,EAC/C,QAAQ,IAAI,EACZ,QAAQ,UAAU,EAClB;IAGF,qBACE,6LAAC;QACC,KAAK;QACL,WAAW;QACV,GAAG,KAAK;kBAER;;;;;;AAGP;;AAEA,QAAQ,WAAW,GAAG;AAEtB,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAAE,CAAC,EACvB,QAAQ,EACR,SAAS,EACT,OAAO,MAAM,EACb,UAAU,SAAS,EACnB,SAAS,QAAQ,EACjB,GAAG,OACJ,EAAE;IACD,MAAM,cAAc;IAEpB,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,MAAM;QACN,IAAI;QACJ,IAAI;QACJ,OAAO;IACT;IAEA,MAAM,WAAW;QACf,SAAS;QACT,OAAO;QACP,SAAS;QACT,QAAQ;QACR,SAAS;QACT,SAAS;QACT,QAAQ;IACV;IAEA,MAAM,UAAU;QACd,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,MAAM;IACR;IAEA,MAAM,UAAU,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACjB,aACA,KAAK,CAAC,KAAK,EACX,QAAQ,CAAC,QAAQ,EACjB,OAAO,CAAC,OAAO,EACf;IAGF,qBACE,6LAAC;QACC,KAAK;QACL,WAAW;QACV,GAAG,KAAK;kBAER;;;;;;AAGP;;AAEA,KAAK,WAAW,GAAG;AAEnB,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAAE,CAAC,EACvB,QAAQ,EACR,SAAS,EACT,SAAS,IAAI,EACb,GAAG,OACJ,EAAE;IACD,MAAM,cAAc;IAEpB,MAAM,gBAAgB,SAClB,kCACA;IAEJ,MAAM,UAAU,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACjB,aACA,eACA;IAGF,MAAM,MAAM,SAAS,SAAS;IAE9B,qBACE,6LAAC;QACC,KAAK;QACL,WAAW;QACV,GAAG,KAAK;kBAER,SAAS,yBAAW,6LAAC;sBAAM;;;;;;;;;;;AAGlC;;AAEA,KAAK,WAAW,GAAG;AAEnB,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAAE,CAAC,EACvB,QAAQ,EACR,SAAS,EACT,UAAU,SAAS,EACnB,WAAW,KAAK,EAChB,GAAG,OACJ,EAAE;IACD,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,QAAQ;QACR,OAAO;IACT;IAEA,MAAM,UAAU,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACjB,aACA,QAAQ,CAAC,QAAQ,EACjB;IAGF,MAAM,gBAAgB,WAAW;QAC/B,QAAQ;QACR,KAAK;IACP,IAAI,CAAC;IAEL,qBACE,6LAAC;QACC,KAAK;QACL,WAAW;QACV,GAAG,aAAa;QAChB,GAAG,KAAK;;YAER;YACA,0BACC,6LAAC;gBAAK,WAAU;0BAAe;;;;;;;;;;;;AAIvC;;AAEA,KAAK,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 555, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i1-d3-repotu/src/components/layout/Footer.js"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport Container from '@/components/ui/Container';\nimport { Text } from '@/components/ui/Typography';\n\nconst Footer = () => {\n  const footerLinks = {\n    product: [\n      { name: 'Features', href: '/#features' },\n      { name: 'Demo', href: '/demo' },\n      { name: 'Pricing', href: '/#pricing' },\n      { name: 'Roadmap', href: '/roadmap' },\n    ],\n    company: [\n      { name: 'About Us', href: '/why-us' },\n      { name: 'Pitch Deck', href: '/pitch-deck' },\n      { name: 'Contact', href: '/contact' },\n      { name: 'Careers', href: '/careers' },\n    ],\n    legal: [\n      { name: 'Privacy Policy', href: '/privacy' },\n      { name: 'Terms of Service', href: '/terms' },\n      { name: 'Cookie Policy', href: '/cookies' },\n      { name: 'GDPR', href: '/gdpr' },\n    ],\n    support: [\n      { name: 'Help Center', href: '/help' },\n      { name: 'Documentation', href: '/docs' },\n      { name: 'API Reference', href: '/api' },\n      { name: 'Status', href: '/status' },\n    ],\n  };\n\n  const socialLinks = [\n    {\n      name: 'Twitter',\n      href: 'https://twitter.com/reportu',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\" />\n        </svg>\n      ),\n    },\n    {\n      name: 'LinkedIn',\n      href: 'https://linkedin.com/company/reportu',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\" />\n        </svg>\n      ),\n    },\n    {\n      name: 'GitHub',\n      href: 'https://github.com/reportu',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z\" />\n        </svg>\n      ),\n    },\n    {\n      name: 'Discord',\n      href: 'https://discord.gg/reportu',\n      icon: (\n        <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419-.0002 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1568 2.4189Z\" />\n        </svg>\n      ),\n    },\n  ];\n\n  return (\n    <footer className=\"bg-neutral-black border-t border-neutral-gray/20\">\n      <Container>\n        <div className=\"py-12\">\n          {/* Main Footer Content */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8\">\n            {/* Brand Section */}\n            <div className=\"lg:col-span-2\">\n              <Link href=\"/\" className=\"flex items-center space-x-3 mb-4\">\n                <div className=\"w-8 h-8 bg-gradient-to-br from-primary-cyan to-primary-blue rounded-lg flex items-center justify-center\">\n                  <svg\n                    className=\"w-5 h-5 text-white\"\n                    fill=\"currentColor\"\n                    viewBox=\"0 0 24 24\"\n                  >\n                    <path d=\"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5\" />\n                  </svg>\n                </div>\n                <span className=\"text-lg font-heading font-bold text-white\">\n                  ReportU\n                </span>\n              </Link>\n              <Text variant=\"muted\" className=\"mb-4 max-w-sm\">\n                Revolutionary cross-border platform for Malaysia and Singapore citizens to efficiently submit offense reports with AI-powered routing.\n              </Text>\n              <div className=\"flex space-x-4\">\n                {socialLinks.map((social) => (\n                  <a\n                    key={social.name}\n                    href={social.href}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"text-neutral-gray hover:text-primary-cyan transition-colors duration-300\"\n                    aria-label={social.name}\n                  >\n                    {social.icon}\n                  </a>\n                ))}\n              </div>\n            </div>\n\n            {/* Footer Links */}\n            <div>\n              <h3 className=\"text-sm font-semibold text-white uppercase tracking-wider mb-4\">\n                Product\n              </h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.product.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-neutral-gray hover:text-white transition-colors duration-300\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            <div>\n              <h3 className=\"text-sm font-semibold text-white uppercase tracking-wider mb-4\">\n                Company\n              </h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.company.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-neutral-gray hover:text-white transition-colors duration-300\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            <div>\n              <h3 className=\"text-sm font-semibold text-white uppercase tracking-wider mb-4\">\n                Legal\n              </h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.legal.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-neutral-gray hover:text-white transition-colors duration-300\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            <div>\n              <h3 className=\"text-sm font-semibold text-white uppercase tracking-wider mb-4\">\n                Support\n              </h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.support.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-neutral-gray hover:text-white transition-colors duration-300\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          </div>\n\n          {/* Bottom Section */}\n          <div className=\"mt-12 pt-8 border-t border-neutral-gray/20 flex flex-col md:flex-row justify-between items-center\">\n            <Text variant=\"muted\" size=\"sm\">\n              © 2024 ReportU. All rights reserved. Built with ❤️ for safer communities.\n            </Text>\n            <div className=\"mt-4 md:mt-0 flex items-center space-x-6\">\n              <Text variant=\"muted\" size=\"sm\">\n                🇲🇾 Malaysia\n              </Text>\n              <Text variant=\"muted\" size=\"sm\">\n                🇸🇬 Singapore\n              </Text>\n            </div>\n          </div>\n        </div>\n      </Container>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,SAAS;IACb,MAAM,cAAc;QAClB,SAAS;YACP;gBAAE,MAAM;gBAAY,MAAM;YAAa;YACvC;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;YAC9B;gBAAE,MAAM;gBAAW,MAAM;YAAY;YACrC;gBAAE,MAAM;gBAAW,MAAM;YAAW;SACrC;QACD,SAAS;YACP;gBAAE,MAAM;gBAAY,MAAM;YAAU;YACpC;gBAAE,MAAM;gBAAc,MAAM;YAAc;YAC1C;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAW,MAAM;YAAW;SACrC;QACD,OAAO;YACL;gBAAE,MAAM;gBAAkB,MAAM;YAAW;YAC3C;gBAAE,MAAM;gBAAoB,MAAM;YAAS;YAC3C;gBAAE,MAAM;gBAAiB,MAAM;YAAW;YAC1C;gBAAE,MAAM;gBAAQ,MAAM;YAAQ;SAC/B;QACD,SAAS;YACP;gBAAE,MAAM;gBAAe,MAAM;YAAQ;YACrC;gBAAE,MAAM;gBAAiB,MAAM;YAAQ;YACvC;gBAAE,MAAM;gBAAiB,MAAM;YAAO;YACtC;gBAAE,MAAM;gBAAU,MAAM;YAAU;SACnC;IACH;IAEA,MAAM,cAAc;QAClB;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAe,SAAQ;0BACnD,cAAA,6LAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAe,SAAQ;0BACnD,cAAA,6LAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAe,SAAQ;0BACnD,cAAA,6LAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;QACA;YACE,MAAM;YACN,MAAM;YACN,oBACE,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAe,SAAQ;0BACnD,cAAA,6LAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;KACD;IAED,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC,uIAAA,CAAA,UAAS;sBACR,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;;0DACvB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,WAAU;oDACV,MAAK;oDACL,SAAQ;8DAER,cAAA,6LAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;0DAGZ,6LAAC;gDAAK,WAAU;0DAA4C;;;;;;;;;;;;kDAI9D,6LAAC,wIAAA,CAAA,OAAI;wCAAC,SAAQ;wCAAQ,WAAU;kDAAgB;;;;;;kDAGhD,6LAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,6LAAC;gDAEC,MAAM,OAAO,IAAI;gDACjB,QAAO;gDACP,KAAI;gDACJ,WAAU;gDACV,cAAY,OAAO,IAAI;0DAEtB,OAAO,IAAI;+CAPP,OAAO,IAAI;;;;;;;;;;;;;;;;0CAcxB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAiE;;;;;;kDAG/E,6LAAC;wCAAG,WAAU;kDACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAYxB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAiE;;;;;;kDAG/E,6LAAC;wCAAG,WAAU;kDACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAYxB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAiE;;;;;;kDAG/E,6LAAC;wCAAG,WAAU;kDACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAYxB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAiE;;;;;;kDAG/E,6LAAC;wCAAG,WAAU;kDACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;kCAc1B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,wIAAA,CAAA,OAAI;gCAAC,SAAQ;gCAAQ,MAAK;0CAAK;;;;;;0CAGhC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,wIAAA,CAAA,OAAI;wCAAC,SAAQ;wCAAQ,MAAK;kDAAK;;;;;;kDAGhC,6LAAC,wIAAA,CAAA,OAAI;wCAAC,SAAQ;wCAAQ,MAAK;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9C;KAxMM;uCA0MS", "debugId": null}}, {"offset": {"line": 1044, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i1-d3-repotu/src/components/sections/HeroSection.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef, useState } from 'react';\nimport Container from '@/components/ui/Container';\nimport Button from '@/components/ui/Button';\nimport { Heading, Text } from '@/components/ui/Typography';\nimport { PlayIcon, ChevronDownIcon } from '@heroicons/react/24/outline';\n\nconst MatrixRain = () => {\n  const canvasRef = useRef(null);\n\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    canvas.width = window.innerWidth;\n    canvas.height = window.innerHeight;\n\n    const matrix = \"REPORTU01MALAYSIA10SINGAPORE01ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789@#$%^&*()\";\n    const matrixArray = matrix.split(\"\");\n\n    const fontSize = 10;\n    const columns = canvas.width / fontSize;\n    const drops = [];\n\n    for (let x = 0; x < columns; x++) {\n      drops[x] = 1;\n    }\n\n    const draw = () => {\n      ctx.fillStyle = 'rgba(0, 0, 0, 0.04)';\n      ctx.fillRect(0, 0, canvas.width, canvas.height);\n\n      ctx.fillStyle = '#00FFFF';\n      ctx.font = fontSize + 'px monospace';\n\n      for (let i = 0; i < drops.length; i++) {\n        const text = matrixArray[Math.floor(Math.random() * matrixArray.length)];\n        ctx.fillText(text, i * fontSize, drops[i] * fontSize);\n\n        if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {\n          drops[i] = 0;\n        }\n        drops[i]++;\n      }\n    };\n\n    const interval = setInterval(draw, 35);\n\n    const handleResize = () => {\n      canvas.width = window.innerWidth;\n      canvas.height = window.innerHeight;\n    };\n\n    window.addEventListener('resize', handleResize);\n\n    return () => {\n      clearInterval(interval);\n      window.removeEventListener('resize', handleResize);\n    };\n  }, []);\n\n  return (\n    <canvas\n      ref={canvasRef}\n      className=\"absolute inset-0 opacity-10 pointer-events-none\"\n      style={{ zIndex: 1 }}\n    />\n  );\n};\n\nconst TypewriterText = ({ text, delay = 100, className = \"\" }) => {\n  const [displayText, setDisplayText] = useState('');\n  const [currentIndex, setCurrentIndex] = useState(0);\n\n  useEffect(() => {\n    if (currentIndex < text.length) {\n      const timeout = setTimeout(() => {\n        setDisplayText(prev => prev + text[currentIndex]);\n        setCurrentIndex(prev => prev + 1);\n      }, delay);\n\n      return () => clearTimeout(timeout);\n    }\n  }, [currentIndex, text, delay]);\n\n  return (\n    <span className={className}>\n      {displayText}\n      <span className=\"animate-pulse\">|</span>\n    </span>\n  );\n};\n\nconst MiniDemo = () => {\n  const [step, setStep] = useState(0);\n  const steps = [\n    { icon: '📱', text: 'Report Submitted', color: 'text-secondary-green' },\n    { icon: '🤖', text: 'AI Processing...', color: 'text-primary-cyan' },\n    { icon: '🏛️', text: 'Routed to Authority', color: 'text-accent-gold' },\n    { icon: '✅', text: 'Case Opened', color: 'text-secondary-green' },\n  ];\n\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setStep(prev => (prev + 1) % steps.length);\n    }, 2000);\n\n    return () => clearInterval(interval);\n  }, [steps.length]);\n\n  return (\n    <div className=\"bg-neutral-dark/50 backdrop-blur-sm border border-primary-cyan/30 rounded-xl p-6 max-w-sm\">\n      <div className=\"text-center\">\n        <div className=\"text-4xl mb-2 animate-bounce\">\n          {steps[step].icon}\n        </div>\n        <Text className={`font-medium ${steps[step].color}`}>\n          {steps[step].text}\n        </Text>\n        <div className=\"flex justify-center mt-3 space-x-1\">\n          {steps.map((_, index) => (\n            <div\n              key={index}\n              className={`w-2 h-2 rounded-full transition-all duration-300 ${\n                index === step ? 'bg-primary-cyan' : 'bg-neutral-gray'\n              }`}\n            />\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nconst HeroSection = () => {\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    setIsVisible(true);\n  }, []);\n\n  const scrollToNext = () => {\n    window.scrollTo({\n      top: window.innerHeight,\n      behavior: 'smooth'\n    });\n  };\n\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n      {/* Matrix Rain Background */}\n      <MatrixRain />\n      \n      {/* Gradient Overlay */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-neutral-black/80 via-transparent to-primary-blue/20\" style={{ zIndex: 2 }} />\n      \n      {/* Cyber Grid */}\n      <div className=\"absolute inset-0 cyber-grid opacity-20\" style={{ zIndex: 3 }} />\n\n      <Container className=\"relative z-10 text-center\">\n        <div className={`transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\n          {/* Main Headline */}\n          <div className=\"mb-8\">\n            <Heading level={1} className=\"mb-4\">\n              <TypewriterText \n                text=\"Cross-Border Offense Reporting\"\n                delay={80}\n                className=\"bg-gradient-to-r from-primary-cyan via-primary-blue to-primary-purple bg-clip-text text-transparent\"\n              />\n            </Heading>\n            <Heading level={2} className=\"text-neutral-white/90 font-normal\">\n              Revolutionizing Public Safety in{' '}\n              <span className=\"text-secondary-green font-bold\">Malaysia</span>\n              {' & '}\n              <span className=\"text-secondary-pink font-bold\">Singapore</span>\n            </Heading>\n          </div>\n\n          {/* Subtitle */}\n          <div className=\"mb-12 max-w-3xl mx-auto\">\n            <Text size=\"xl\" className=\"text-neutral-light leading-relaxed\">\n              AI-powered platform that instantly routes your offense reports to the right authorities. \n              Submit evidence, track progress, and make your community safer with just a few taps.\n            </Text>\n          </div>\n\n          {/* CTA Buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-6 justify-center items-center mb-16\">\n            <Button \n              variant=\"primary\" \n              size=\"xl\" \n              className=\"group glow transform hover:scale-105 transition-all duration-300\"\n            >\n              <PlayIcon className=\"w-6 h-6 mr-2 group-hover:animate-pulse\" />\n              Try Live Demo\n            </Button>\n            <Button \n              variant=\"secondary\" \n              size=\"xl\"\n              className=\"group transform hover:scale-105 transition-all duration-300\"\n            >\n              Watch Pitch Deck\n              <ChevronDownIcon className=\"w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-300\" />\n            </Button>\n          </div>\n\n          {/* Stats Row */}\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mb-16\">\n            <div className=\"text-center\">\n              <div className=\"text-4xl font-bold text-primary-cyan mb-2\">39M+</div>\n              <Text variant=\"muted\">Potential Users</Text>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-4xl font-bold text-accent-neon mb-2\">&lt;30s</div>\n              <Text variant=\"muted\">Report Submission</Text>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-4xl font-bold text-secondary-green mb-2\">24/7</div>\n              <Text variant=\"muted\">AI-Powered Routing</Text>\n            </div>\n          </div>\n\n          {/* Mini Demo */}\n          <div className=\"flex justify-center mb-16\">\n            <MiniDemo />\n          </div>\n\n          {/* Trust Indicators */}\n          <div className=\"flex flex-wrap justify-center items-center gap-8 opacity-60\">\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-8 h-8 bg-secondary-green rounded-full flex items-center justify-center\">\n                <span className=\"text-xs font-bold text-neutral-black\">🔒</span>\n              </div>\n              <Text size=\"sm\" variant=\"muted\">End-to-End Encrypted</Text>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-8 h-8 bg-primary-cyan rounded-full flex items-center justify-center\">\n                <span className=\"text-xs font-bold text-neutral-black\">⚡</span>\n              </div>\n              <Text size=\"sm\" variant=\"muted\">Real-time Processing</Text>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-8 h-8 bg-accent-gold rounded-full flex items-center justify-center\">\n                <span className=\"text-xs font-bold text-neutral-black\">🏛️</span>\n              </div>\n              <Text size=\"sm\" variant=\"muted\">Government Ready</Text>\n            </div>\n          </div>\n        </div>\n\n        {/* Scroll Indicator */}\n        <button\n          onClick={scrollToNext}\n          className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 text-primary-cyan hover:text-primary-blue transition-colors duration-300 animate-bounce\"\n          aria-label=\"Scroll to next section\"\n        >\n          <ChevronDownIcon className=\"w-8 h-8\" />\n        </button>\n      </Container>\n    </section>\n  );\n};\n\nexport default HeroSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;;;AANA;;;;;;AAQA,MAAM,aAAa;;IACjB,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEzB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM,SAAS,UAAU,OAAO;YAChC,IAAI,CAAC,QAAQ;YAEb,MAAM,MAAM,OAAO,UAAU,CAAC;YAC9B,OAAO,KAAK,GAAG,OAAO,UAAU;YAChC,OAAO,MAAM,GAAG,OAAO,WAAW;YAElC,MAAM,SAAS;YACf,MAAM,cAAc,OAAO,KAAK,CAAC;YAEjC,MAAM,WAAW;YACjB,MAAM,UAAU,OAAO,KAAK,GAAG;YAC/B,MAAM,QAAQ,EAAE;YAEhB,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,IAAK;gBAChC,KAAK,CAAC,EAAE,GAAG;YACb;YAEA,MAAM;6CAAO;oBACX,IAAI,SAAS,GAAG;oBAChB,IAAI,QAAQ,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;oBAE9C,IAAI,SAAS,GAAG;oBAChB,IAAI,IAAI,GAAG,WAAW;oBAEtB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;wBACrC,MAAM,OAAO,WAAW,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,YAAY,MAAM,EAAE;wBACxE,IAAI,QAAQ,CAAC,MAAM,IAAI,UAAU,KAAK,CAAC,EAAE,GAAG;wBAE5C,IAAI,KAAK,CAAC,EAAE,GAAG,WAAW,OAAO,MAAM,IAAI,KAAK,MAAM,KAAK,OAAO;4BAChE,KAAK,CAAC,EAAE,GAAG;wBACb;wBACA,KAAK,CAAC,EAAE;oBACV;gBACF;;YAEA,MAAM,WAAW,YAAY,MAAM;YAEnC,MAAM;qDAAe;oBACnB,OAAO,KAAK,GAAG,OAAO,UAAU;oBAChC,OAAO,MAAM,GAAG,OAAO,WAAW;gBACpC;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAElC;wCAAO;oBACL,cAAc;oBACd,OAAO,mBAAmB,CAAC,UAAU;gBACvC;;QACF;+BAAG,EAAE;IAEL,qBACE,6LAAC;QACC,KAAK;QACL,WAAU;QACV,OAAO;YAAE,QAAQ;QAAE;;;;;;AAGzB;GA9DM;KAAA;AAgEN,MAAM,iBAAiB,CAAC,EAAE,IAAI,EAAE,QAAQ,GAAG,EAAE,YAAY,EAAE,EAAE;;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,eAAe,KAAK,MAAM,EAAE;gBAC9B,MAAM,UAAU;wDAAW;wBACzB;gEAAe,CAAA,OAAQ,OAAO,IAAI,CAAC,aAAa;;wBAChD;gEAAgB,CAAA,OAAQ,OAAO;;oBACjC;uDAAG;gBAEH;gDAAO,IAAM,aAAa;;YAC5B;QACF;mCAAG;QAAC;QAAc;QAAM;KAAM;IAE9B,qBACE,6LAAC;QAAK,WAAW;;YACd;0BACD,6LAAC;gBAAK,WAAU;0BAAgB;;;;;;;;;;;;AAGtC;IArBM;MAAA;AAuBN,MAAM,WAAW;;IACf,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,QAAQ;QACZ;YAAE,MAAM;YAAM,MAAM;YAAoB,OAAO;QAAuB;QACtE;YAAE,MAAM;YAAM,MAAM;YAAoB,OAAO;QAAoB;QACnE;YAAE,MAAM;YAAO,MAAM;YAAuB,OAAO;QAAmB;QACtE;YAAE,MAAM;YAAK,MAAM;YAAe,OAAO;QAAuB;KACjE;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,MAAM,WAAW;+CAAY;oBAC3B;uDAAQ,CAAA,OAAQ,CAAC,OAAO,CAAC,IAAI,MAAM,MAAM;;gBAC3C;8CAAG;YAEH;sCAAO,IAAM,cAAc;;QAC7B;6BAAG;QAAC,MAAM,MAAM;KAAC;IAEjB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACZ,KAAK,CAAC,KAAK,CAAC,IAAI;;;;;;8BAEnB,6LAAC,wIAAA,CAAA,OAAI;oBAAC,WAAW,CAAC,YAAY,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE;8BAChD,KAAK,CAAC,KAAK,CAAC,IAAI;;;;;;8BAEnB,6LAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,GAAG,sBACb,6LAAC;4BAEC,WAAW,CAAC,iDAAiD,EAC3D,UAAU,OAAO,oBAAoB,mBACrC;2BAHG;;;;;;;;;;;;;;;;;;;;;AAUnB;IAvCM;MAAA;AAyCN,MAAM,cAAc;;IAClB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,aAAa;QACf;gCAAG,EAAE;IAEL,MAAM,eAAe;QACnB,OAAO,QAAQ,CAAC;YACd,KAAK,OAAO,WAAW;YACvB,UAAU;QACZ;IACF;IAEA,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;;;;;0BAGD,6LAAC;gBAAI,WAAU;gBAA8F,OAAO;oBAAE,QAAQ;gBAAE;;;;;;0BAGhI,6LAAC;gBAAI,WAAU;gBAAyC,OAAO;oBAAE,QAAQ;gBAAE;;;;;;0BAE3E,6LAAC,uIAAA,CAAA,UAAS;gBAAC,WAAU;;kCACnB,6LAAC;wBAAI,WAAW,CAAC,6BAA6B,EAAE,YAAY,8BAA8B,4BAA4B;;0CAEpH,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,wIAAA,CAAA,UAAO;wCAAC,OAAO;wCAAG,WAAU;kDAC3B,cAAA,6LAAC;4CACC,MAAK;4CACL,OAAO;4CACP,WAAU;;;;;;;;;;;kDAGd,6LAAC,wIAAA,CAAA,UAAO;wCAAC,OAAO;wCAAG,WAAU;;4CAAoC;4CAC9B;0DACjC,6LAAC;gDAAK,WAAU;0DAAiC;;;;;;4CAChD;0DACD,6LAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;0CAKpD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,wIAAA,CAAA,OAAI;oCAAC,MAAK;oCAAK,WAAU;8CAAqC;;;;;;;;;;;0CAOjE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,UAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;;0DAEV,6LAAC,kNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAA2C;;;;;;;kDAGjE,6LAAC,oIAAA,CAAA,UAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;;4CACX;0DAEC,6LAAC,gOAAA,CAAA,kBAAe;gDAAC,WAAU;;;;;;;;;;;;;;;;;;0CAK/B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAA4C;;;;;;0DAC3D,6LAAC,wIAAA,CAAA,OAAI;gDAAC,SAAQ;0DAAQ;;;;;;;;;;;;kDAExB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAA2C;;;;;;0DAC1D,6LAAC,wIAAA,CAAA,OAAI;gDAAC,SAAQ;0DAAQ;;;;;;;;;;;;kDAExB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAA+C;;;;;;0DAC9D,6LAAC,wIAAA,CAAA,OAAI;gDAAC,SAAQ;0DAAQ;;;;;;;;;;;;;;;;;;0CAK1B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;;;;;;;;;;0CAIH,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAAuC;;;;;;;;;;;0DAEzD,6LAAC,wIAAA,CAAA,OAAI;gDAAC,MAAK;gDAAK,SAAQ;0DAAQ;;;;;;;;;;;;kDAElC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAAuC;;;;;;;;;;;0DAEzD,6LAAC,wIAAA,CAAA,OAAI;gDAAC,MAAK;gDAAK,SAAQ;0DAAQ;;;;;;;;;;;;kDAElC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAAuC;;;;;;;;;;;0DAEzD,6LAAC,wIAAA,CAAA,OAAI;gDAAC,MAAK;gDAAK,SAAQ;0DAAQ;;;;;;;;;;;;;;;;;;;;;;;;kCAMtC,6LAAC;wBACC,SAAS;wBACT,WAAU;wBACV,cAAW;kCAEX,cAAA,6LAAC,gOAAA,CAAA,kBAAe;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKrC;IA/HM;MAAA;uCAiIS", "debugId": null}}, {"offset": {"line": 1686, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i1-d3-repotu/src/components/ui/Card.js"], "sourcesContent": ["'use client';\n\nimport { forwardRef } from 'react';\nimport { clsx } from 'clsx';\n\nconst Card = forwardRef(({ \n  children, \n  className, \n  variant = 'default',\n  hover = true,\n  glow = false,\n  tilt = false,\n  ...props \n}, ref) => {\n  const baseClasses = 'rounded-xl border backdrop-blur-sm transition-all duration-300';\n  \n  const variants = {\n    default: 'bg-neutral-dark/80 border-neutral-gray/30 text-neutral-white',\n    glass: 'glass border-white/20 text-neutral-white',\n    primary: 'bg-primary-blue/20 border-primary-cyan/50 text-neutral-white',\n    accent: 'bg-accent-neon/10 border-accent-neon/30 text-neutral-white',\n    dark: 'bg-neutral-black/90 border-neutral-dark text-neutral-white',\n  };\n  \n  const hoverEffects = hover ? 'hover:scale-105 hover:shadow-lg' : '';\n  const glowEffect = glow ? 'glow-hover' : '';\n  const tiltEffect = tilt ? 'tilt-3d' : '';\n  \n  const classes = clsx(\n    baseClasses,\n    variants[variant],\n    hoverEffects,\n    glowEffect,\n    tiltEffect,\n    className\n  );\n  \n  return (\n    <div\n      ref={ref}\n      className={classes}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n});\n\nCard.displayName = 'Card';\n\nconst CardHeader = forwardRef(({ children, className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={clsx('p-6 pb-0', className)}\n    {...props}\n  >\n    {children}\n  </div>\n));\n\nCardHeader.displayName = 'CardHeader';\n\nconst CardContent = forwardRef(({ children, className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={clsx('p-6', className)}\n    {...props}\n  >\n    {children}\n  </div>\n));\n\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = forwardRef(({ children, className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={clsx('p-6 pt-0', className)}\n    {...props}\n  >\n    {children}\n  </div>\n));\n\nCardFooter.displayName = 'CardFooter';\n\nconst CardTitle = forwardRef(({ children, className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={clsx('text-xl font-heading font-semibold leading-none tracking-tight', className)}\n    {...props}\n  >\n    {children}\n  </h3>\n));\n\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = forwardRef(({ children, className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={clsx('text-sm text-neutral-gray leading-relaxed', className)}\n    {...props}\n  >\n    {children}\n  </p>\n));\n\nCardDescription.displayName = 'CardDescription';\n\nexport { Card, CardHeader, CardContent, CardFooter, CardTitle, CardDescription };\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OAAE,CAAC,EACvB,QAAQ,EACR,SAAS,EACT,UAAU,SAAS,EACnB,QAAQ,IAAI,EACZ,OAAO,KAAK,EACZ,OAAO,KAAK,EACZ,GAAG,OACJ,EAAE;IACD,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,OAAO;QACP,SAAS;QACT,QAAQ;QACR,MAAM;IACR;IAEA,MAAM,eAAe,QAAQ,oCAAoC;IACjE,MAAM,aAAa,OAAO,eAAe;IACzC,MAAM,aAAa,OAAO,YAAY;IAEtC,MAAM,UAAU,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EACjB,aACA,QAAQ,CAAC,QAAQ,EACjB,cACA,YACA,YACA;IAGF,qBACE,6LAAC;QACC,KAAK;QACL,WAAW;QACV,GAAG,KAAK;kBAER;;;;;;AAGP;;AAEA,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAAE,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAChE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE,YAAY;QAC3B,GAAG,KAAK;kBAER;;;;;;;AAIL,WAAW,WAAW,GAAG;AAEzB,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAAE,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACjE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE,OAAO;QACtB,GAAG,KAAK;kBAER;;;;;;;AAIL,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAAE,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAChE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE,YAAY;QAC3B,GAAG,KAAK;kBAER;;;;;;;AAIL,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAAE,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC/D,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE,kEAAkE;QACjF,GAAG,KAAK;kBAER;;;;;;;AAIL,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,SAAE,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACrE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE,6CAA6C;QAC5D,GAAG,KAAK;kBAER;;;;;;;AAIL,gBAAgB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1810, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i1-d3-repotu/src/components/sections/ProblemSolutionSection.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef, useState } from 'react';\nimport { gsap } from 'gsap';\nimport { ScrollTrigger } from 'gsap/ScrollTrigger';\nimport Container from '@/components/ui/Container';\nimport { Card, CardContent } from '@/components/ui/Card';\nimport { Heading, Text } from '@/components/ui/Typography';\nimport { \n  ExclamationTriangleIcon, \n  ClockIcon, \n  QuestionMarkCircleIcon,\n  CheckCircleIcon,\n  BoltIcon,\n  ShieldCheckIcon \n} from '@heroicons/react/24/outline';\n\n// Register GSAP plugins\nif (typeof window !== 'undefined') {\n  gsap.registerPlugin(ScrollTrigger);\n}\n\nconst StatCard = ({ icon: Icon, number, label, description, delay = 0 }) => {\n  const [count, setCount] = useState(0);\n  const cardRef = useRef(null);\n\n  useEffect(() => {\n    const element = cardRef.current;\n    if (!element) return;\n\n    const ctx = gsap.context(() => {\n      // Animate the card entrance\n      gsap.fromTo(element, \n        { \n          opacity: 0, \n          y: 50,\n          scale: 0.8\n        },\n        {\n          opacity: 1,\n          y: 0,\n          scale: 1,\n          duration: 0.8,\n          delay: delay,\n          ease: \"back.out(1.7)\",\n          scrollTrigger: {\n            trigger: element,\n            start: \"top 80%\",\n            end: \"bottom 20%\",\n            toggleActions: \"play none none reverse\"\n          }\n        }\n      );\n\n      // Animate the number counting\n      gsap.to({ value: 0 }, {\n        value: parseInt(number),\n        duration: 2,\n        delay: delay + 0.5,\n        ease: \"power2.out\",\n        onUpdate: function() {\n          setCount(Math.floor(this.targets()[0].value));\n        },\n        scrollTrigger: {\n          trigger: element,\n          start: \"top 80%\",\n          toggleActions: \"play none none reverse\"\n        }\n      });\n    }, element);\n\n    return () => ctx.revert();\n  }, [number, delay]);\n\n  return (\n    <Card \n      ref={cardRef}\n      variant=\"glass\" \n      className=\"text-center p-6 hover:scale-105 transition-transform duration-300\"\n      glow\n    >\n      <CardContent className=\"space-y-4\">\n        <div className=\"flex justify-center\">\n          <div className=\"w-16 h-16 bg-gradient-to-br from-primary-cyan to-primary-blue rounded-full flex items-center justify-center\">\n            <Icon className=\"w-8 h-8 text-white\" />\n          </div>\n        </div>\n        <div className=\"text-4xl font-bold text-primary-cyan\">\n          {count}{number.includes('%') ? '%' : number.includes('M') ? 'M+' : ''}\n        </div>\n        <Heading level={4} className=\"text-white\">\n          {label}\n        </Heading>\n        <Text variant=\"muted\" size=\"sm\">\n          {description}\n        </Text>\n      </CardContent>\n    </Card>\n  );\n};\n\nconst ProblemCard = ({ icon: Icon, title, description, delay = 0 }) => {\n  const cardRef = useRef(null);\n\n  useEffect(() => {\n    const element = cardRef.current;\n    if (!element) return;\n\n    const ctx = gsap.context(() => {\n      gsap.fromTo(element,\n        { \n          opacity: 0, \n          x: -100,\n          rotationY: -15\n        },\n        {\n          opacity: 1,\n          x: 0,\n          rotationY: 0,\n          duration: 1,\n          delay: delay,\n          ease: \"power3.out\",\n          scrollTrigger: {\n            trigger: element,\n            start: \"top 85%\",\n            end: \"bottom 15%\",\n            toggleActions: \"play none none reverse\"\n          }\n        }\n      );\n    }, element);\n\n    return () => ctx.revert();\n  }, [delay]);\n\n  return (\n    <Card \n      ref={cardRef}\n      variant=\"dark\" \n      className=\"p-6 border-l-4 border-secondary-pink hover:border-secondary-orange transition-colors duration-300\"\n    >\n      <CardContent className=\"flex items-start space-x-4\">\n        <div className=\"flex-shrink-0\">\n          <div className=\"w-12 h-12 bg-secondary-pink/20 rounded-lg flex items-center justify-center\">\n            <Icon className=\"w-6 h-6 text-secondary-pink\" />\n          </div>\n        </div>\n        <div className=\"flex-1\">\n          <Heading level={4} className=\"text-white mb-2\">\n            {title}\n          </Heading>\n          <Text variant=\"muted\">\n            {description}\n          </Text>\n        </div>\n      </CardContent>\n    </Card>\n  );\n};\n\nconst SolutionCard = ({ icon: Icon, title, description, delay = 0 }) => {\n  const cardRef = useRef(null);\n\n  useEffect(() => {\n    const element = cardRef.current;\n    if (!element) return;\n\n    const ctx = gsap.context(() => {\n      gsap.fromTo(element,\n        { \n          opacity: 0, \n          x: 100,\n          rotationY: 15\n        },\n        {\n          opacity: 1,\n          x: 0,\n          rotationY: 0,\n          duration: 1,\n          delay: delay,\n          ease: \"power3.out\",\n          scrollTrigger: {\n            trigger: element,\n            start: \"top 85%\",\n            end: \"bottom 15%\",\n            toggleActions: \"play none none reverse\"\n          }\n        }\n      );\n    }, element);\n\n    return () => ctx.revert();\n  }, [delay]);\n\n  return (\n    <Card \n      ref={cardRef}\n      variant=\"primary\" \n      className=\"p-6 border-l-4 border-secondary-green hover:border-accent-neon transition-colors duration-300\"\n      glow\n    >\n      <CardContent className=\"flex items-start space-x-4\">\n        <div className=\"flex-shrink-0\">\n          <div className=\"w-12 h-12 bg-secondary-green/20 rounded-lg flex items-center justify-center\">\n            <Icon className=\"w-6 h-6 text-secondary-green\" />\n          </div>\n        </div>\n        <div className=\"flex-1\">\n          <Heading level={4} className=\"text-white mb-2\">\n            {title}\n          </Heading>\n          <Text variant=\"muted\">\n            {description}\n          </Text>\n        </div>\n      </CardContent>\n    </Card>\n  );\n};\n\nconst ProblemSolutionSection = () => {\n  const sectionRef = useRef(null);\n  const titleRef = useRef(null);\n\n  useEffect(() => {\n    const section = sectionRef.current;\n    const title = titleRef.current;\n    if (!section || !title) return;\n\n    const ctx = gsap.context(() => {\n      // Parallax effect for the section\n      gsap.to(section, {\n        yPercent: -50,\n        ease: \"none\",\n        scrollTrigger: {\n          trigger: section,\n          start: \"top bottom\",\n          end: \"bottom top\",\n          scrub: true\n        }\n      });\n\n      // Title animation\n      gsap.fromTo(title,\n        { \n          opacity: 0, \n          y: 100,\n          scale: 0.8\n        },\n        {\n          opacity: 1,\n          y: 0,\n          scale: 1,\n          duration: 1.2,\n          ease: \"power3.out\",\n          scrollTrigger: {\n            trigger: title,\n            start: \"top 80%\",\n            end: \"bottom 20%\",\n            toggleActions: \"play none none reverse\"\n          }\n        }\n      );\n    }, section);\n\n    return () => ctx.revert();\n  }, []);\n\n  const problems = [\n    {\n      icon: ExclamationTriangleIcon,\n      title: \"Complex Reporting Processes\",\n      description: \"Citizens struggle with navigating multiple platforms and departments, leading to confusion and delayed responses to critical incidents.\"\n    },\n    {\n      icon: ClockIcon,\n      title: \"Time-Consuming Procedures\",\n      description: \"Current systems require extensive paperwork and multiple visits, discouraging people from reporting important offenses.\"\n    },\n    {\n      icon: QuestionMarkCircleIcon,\n      title: \"Authority Confusion\",\n      description: \"Uncertainty about which department handles specific types of offenses creates barriers to effective reporting.\"\n    }\n  ];\n\n  const solutions = [\n    {\n      icon: CheckCircleIcon,\n      title: \"One-Click Reporting\",\n      description: \"Submit any offense report with just a few taps, including photos, videos, and location data automatically.\"\n    },\n    {\n      icon: BoltIcon,\n      title: \"AI-Powered Routing\",\n      description: \"Smart categorization instantly directs reports to the correct authorities in Malaysia or Singapore.\"\n    },\n    {\n      icon: ShieldCheckIcon,\n      title: \"Real-Time Tracking\",\n      description: \"Get live updates on your report status and see exactly what actions are being taken by authorities.\"\n    }\n  ];\n\n  const stats = [\n    {\n      icon: ClockIcon,\n      number: \"73\",\n      label: \"Average Report Time\",\n      description: \"Minutes wasted on current systems\"\n    },\n    {\n      icon: ExclamationTriangleIcon,\n      number: \"45\",\n      label: \"Unreported Incidents\",\n      description: \"Percentage due to complexity\"\n    },\n    {\n      icon: QuestionMarkCircleIcon,\n      number: \"67\",\n      label: \"Wrong Department\",\n      description: \"Reports sent to incorrect authorities\"\n    }\n  ];\n\n  return (\n    <section ref={sectionRef} className=\"py-24 bg-gradient-to-b from-neutral-black to-neutral-dark relative overflow-hidden\">\n      {/* Background Effects */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute top-1/4 left-1/4 w-96 h-96 bg-primary-cyan rounded-full filter blur-3xl\"></div>\n        <div className=\"absolute bottom-1/4 right-1/4 w-96 h-96 bg-primary-purple rounded-full filter blur-3xl\"></div>\n      </div>\n\n      <Container className=\"relative z-10\">\n        {/* Section Title */}\n        <div ref={titleRef} className=\"text-center mb-20\">\n          <Heading level={2} className=\"mb-6\" gradient>\n            The Problem We're Solving\n          </Heading>\n          <Text size=\"xl\" className=\"max-w-3xl mx-auto text-neutral-light\">\n            Current offense reporting systems are broken. We're building the future of civic engagement \n            with AI-powered, cross-border solutions that actually work.\n          </Text>\n        </div>\n\n        {/* Current Problems Stats */}\n        <div className=\"mb-20\">\n          <Heading level={3} className=\"text-center mb-12 text-secondary-pink\">\n            Current System Problems\n          </Heading>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mb-16\">\n            {stats.map((stat, index) => (\n              <StatCard\n                key={stat.label}\n                icon={stat.icon}\n                number={stat.number}\n                label={stat.label}\n                description={stat.description}\n                delay={index * 0.2}\n              />\n            ))}\n          </div>\n        </div>\n\n        {/* Problems vs Solutions */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16 mb-20\">\n          {/* Problems Column */}\n          <div>\n            <Heading level={3} className=\"text-center mb-8 text-secondary-pink\">\n              Current Problems\n            </Heading>\n            <div className=\"space-y-6\">\n              {problems.map((problem, index) => (\n                <ProblemCard\n                  key={problem.title}\n                  icon={problem.icon}\n                  title={problem.title}\n                  description={problem.description}\n                  delay={index * 0.3}\n                />\n              ))}\n            </div>\n          </div>\n\n          {/* Solutions Column */}\n          <div>\n            <Heading level={3} className=\"text-center mb-8 text-secondary-green\">\n              Our Solutions\n            </Heading>\n            <div className=\"space-y-6\">\n              {solutions.map((solution, index) => (\n                <SolutionCard\n                  key={solution.title}\n                  icon={solution.icon}\n                  title={solution.title}\n                  description={solution.description}\n                  delay={index * 0.3 + 0.5}\n                />\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Call to Action */}\n        <div className=\"text-center\">\n          <Card variant=\"glass\" className=\"max-w-2xl mx-auto p-8\" glow>\n            <CardContent>\n              <Heading level={3} className=\"mb-4 text-accent-neon\">\n                Ready to Experience the Difference?\n              </Heading>\n              <Text className=\"mb-6 text-neutral-light\">\n                See how ReportU transforms offense reporting from a 73-minute ordeal \n                into a 30-second solution with real results.\n              </Text>\n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                <button className=\"px-8 py-3 bg-primary-blue hover:bg-primary-cyan text-white rounded-lg font-medium transition-all duration-300 transform hover:scale-105 glow\">\n                  Try Interactive Demo\n                </button>\n                <button className=\"px-8 py-3 border-2 border-primary-cyan text-primary-cyan hover:bg-primary-cyan hover:text-neutral-black rounded-lg font-medium transition-all duration-300\">\n                  View Case Studies\n                </button>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </Container>\n    </section>\n  );\n};\n\nexport default ProblemSolutionSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;;AAiBA,wBAAwB;AACxB,wCAAmC;IACjC,gJAAA,CAAA,OAAI,CAAC,cAAc,CAAC,wIAAA,CAAA,gBAAa;AACnC;AAEA,MAAM,WAAW,CAAC,EAAE,MAAM,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,QAAQ,CAAC,EAAE;;IACrE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,MAAM,UAAU,QAAQ,OAAO;YAC/B,IAAI,CAAC,SAAS;YAEd,MAAM,MAAM,gJAAA,CAAA,OAAI,CAAC,OAAO;0CAAC;oBACvB,4BAA4B;oBAC5B,gJAAA,CAAA,OAAI,CAAC,MAAM,CAAC,SACV;wBACE,SAAS;wBACT,GAAG;wBACH,OAAO;oBACT,GACA;wBACE,SAAS;wBACT,GAAG;wBACH,OAAO;wBACP,UAAU;wBACV,OAAO;wBACP,MAAM;wBACN,eAAe;4BACb,SAAS;4BACT,OAAO;4BACP,KAAK;4BACL,eAAe;wBACjB;oBACF;oBAGF,8BAA8B;oBAC9B,gJAAA,CAAA,OAAI,CAAC,EAAE,CAAC;wBAAE,OAAO;oBAAE,GAAG;wBACpB,OAAO,SAAS;wBAChB,UAAU;wBACV,OAAO,QAAQ;wBACf,MAAM;wBACN,QAAQ;sDAAE;gCACR,SAAS,KAAK,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,KAAK;4BAC7C;;wBACA,eAAe;4BACb,SAAS;4BACT,OAAO;4BACP,eAAe;wBACjB;oBACF;gBACF;yCAAG;YAEH;sCAAO,IAAM,IAAI,MAAM;;QACzB;6BAAG;QAAC;QAAQ;KAAM;IAElB,qBACE,6LAAC,kIAAA,CAAA,OAAI;QACH,KAAK;QACL,SAAQ;QACR,WAAU;QACV,IAAI;kBAEJ,cAAA,6LAAC,kIAAA,CAAA,cAAW;YAAC,WAAU;;8BACrB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;;;;;;;;;;;;;;;;8BAGpB,6LAAC;oBAAI,WAAU;;wBACZ;wBAAO,OAAO,QAAQ,CAAC,OAAO,MAAM,OAAO,QAAQ,CAAC,OAAO,OAAO;;;;;;;8BAErE,6LAAC,wIAAA,CAAA,UAAO;oBAAC,OAAO;oBAAG,WAAU;8BAC1B;;;;;;8BAEH,6LAAC,wIAAA,CAAA,OAAI;oBAAC,SAAQ;oBAAQ,MAAK;8BACxB;;;;;;;;;;;;;;;;;AAKX;GA7EM;KAAA;AA+EN,MAAM,cAAc,CAAC,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,WAAW,EAAE,QAAQ,CAAC,EAAE;;IAChE,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,UAAU,QAAQ,OAAO;YAC/B,IAAI,CAAC,SAAS;YAEd,MAAM,MAAM,gJAAA,CAAA,OAAI,CAAC,OAAO;6CAAC;oBACvB,gJAAA,CAAA,OAAI,CAAC,MAAM,CAAC,SACV;wBACE,SAAS;wBACT,GAAG,CAAC;wBACJ,WAAW,CAAC;oBACd,GACA;wBACE,SAAS;wBACT,GAAG;wBACH,WAAW;wBACX,UAAU;wBACV,OAAO;wBACP,MAAM;wBACN,eAAe;4BACb,SAAS;4BACT,OAAO;4BACP,KAAK;4BACL,eAAe;wBACjB;oBACF;gBAEJ;4CAAG;YAEH;yCAAO,IAAM,IAAI,MAAM;;QACzB;gCAAG;QAAC;KAAM;IAEV,qBACE,6LAAC,kIAAA,CAAA,OAAI;QACH,KAAK;QACL,SAAQ;QACR,WAAU;kBAEV,cAAA,6LAAC,kIAAA,CAAA,cAAW;YAAC,WAAU;;8BACrB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;;;;;;;;;;;;;;;;8BAGpB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,wIAAA,CAAA,UAAO;4BAAC,OAAO;4BAAG,WAAU;sCAC1B;;;;;;sCAEH,6LAAC,wIAAA,CAAA,OAAI;4BAAC,SAAQ;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAMb;IAzDM;MAAA;AA2DN,MAAM,eAAe,CAAC,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,WAAW,EAAE,QAAQ,CAAC,EAAE;;IACjE,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,UAAU,QAAQ,OAAO;YAC/B,IAAI,CAAC,SAAS;YAEd,MAAM,MAAM,gJAAA,CAAA,OAAI,CAAC,OAAO;8CAAC;oBACvB,gJAAA,CAAA,OAAI,CAAC,MAAM,CAAC,SACV;wBACE,SAAS;wBACT,GAAG;wBACH,WAAW;oBACb,GACA;wBACE,SAAS;wBACT,GAAG;wBACH,WAAW;wBACX,UAAU;wBACV,OAAO;wBACP,MAAM;wBACN,eAAe;4BACb,SAAS;4BACT,OAAO;4BACP,KAAK;4BACL,eAAe;wBACjB;oBACF;gBAEJ;6CAAG;YAEH;0CAAO,IAAM,IAAI,MAAM;;QACzB;iCAAG;QAAC;KAAM;IAEV,qBACE,6LAAC,kIAAA,CAAA,OAAI;QACH,KAAK;QACL,SAAQ;QACR,WAAU;QACV,IAAI;kBAEJ,cAAA,6LAAC,kIAAA,CAAA,cAAW;YAAC,WAAU;;8BACrB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;;;;;;;;;;;;;;;;8BAGpB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,wIAAA,CAAA,UAAO;4BAAC,OAAO;4BAAG,WAAU;sCAC1B;;;;;;sCAEH,6LAAC,wIAAA,CAAA,OAAI;4BAAC,SAAQ;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAMb;IA1DM;MAAA;AA4DN,MAAM,yBAAyB;;IAC7B,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1B,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAExB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,MAAM,UAAU,WAAW,OAAO;YAClC,MAAM,QAAQ,SAAS,OAAO;YAC9B,IAAI,CAAC,WAAW,CAAC,OAAO;YAExB,MAAM,MAAM,gJAAA,CAAA,OAAI,CAAC,OAAO;wDAAC;oBACvB,kCAAkC;oBAClC,gJAAA,CAAA,OAAI,CAAC,EAAE,CAAC,SAAS;wBACf,UAAU,CAAC;wBACX,MAAM;wBACN,eAAe;4BACb,SAAS;4BACT,OAAO;4BACP,KAAK;4BACL,OAAO;wBACT;oBACF;oBAEA,kBAAkB;oBAClB,gJAAA,CAAA,OAAI,CAAC,MAAM,CAAC,OACV;wBACE,SAAS;wBACT,GAAG;wBACH,OAAO;oBACT,GACA;wBACE,SAAS;wBACT,GAAG;wBACH,OAAO;wBACP,UAAU;wBACV,MAAM;wBACN,eAAe;4BACb,SAAS;4BACT,OAAO;4BACP,KAAK;4BACL,eAAe;wBACjB;oBACF;gBAEJ;uDAAG;YAEH;oDAAO,IAAM,IAAI,MAAM;;QACzB;2CAAG,EAAE;IAEL,MAAM,WAAW;QACf;YACE,MAAM,gPAAA,CAAA,0BAAuB;YAC7B,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,oNAAA,CAAA,YAAS;YACf,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,8OAAA,CAAA,yBAAsB;YAC5B,OAAO;YACP,aAAa;QACf;KACD;IAED,MAAM,YAAY;QAChB;YACE,MAAM,gOAAA,CAAA,kBAAe;YACrB,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,kNAAA,CAAA,WAAQ;YACd,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,gOAAA,CAAA,kBAAe;YACrB,OAAO;YACP,aAAa;QACf;KACD;IAED,MAAM,QAAQ;QACZ;YACE,MAAM,oNAAA,CAAA,YAAS;YACf,QAAQ;YACR,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,gPAAA,CAAA,0BAAuB;YAC7B,QAAQ;YACR,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,8OAAA,CAAA,yBAAsB;YAC5B,QAAQ;YACR,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,6LAAC;QAAQ,KAAK;QAAY,WAAU;;0BAElC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,6LAAC,uIAAA,CAAA,UAAS;gBAAC,WAAU;;kCAEnB,6LAAC;wBAAI,KAAK;wBAAU,WAAU;;0CAC5B,6LAAC,wIAAA,CAAA,UAAO;gCAAC,OAAO;gCAAG,WAAU;gCAAO,QAAQ;0CAAC;;;;;;0CAG7C,6LAAC,wIAAA,CAAA,OAAI;gCAAC,MAAK;gCAAK,WAAU;0CAAuC;;;;;;;;;;;;kCAOnE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,wIAAA,CAAA,UAAO;gCAAC,OAAO;gCAAG,WAAU;0CAAwC;;;;;;0CAGrE,6LAAC;gCAAI,WAAU;0CACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;wCAEC,MAAM,KAAK,IAAI;wCACf,QAAQ,KAAK,MAAM;wCACnB,OAAO,KAAK,KAAK;wCACjB,aAAa,KAAK,WAAW;wCAC7B,OAAO,QAAQ;uCALV,KAAK,KAAK;;;;;;;;;;;;;;;;kCAYvB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;;kDACC,6LAAC,wIAAA,CAAA,UAAO;wCAAC,OAAO;wCAAG,WAAU;kDAAuC;;;;;;kDAGpE,6LAAC;wCAAI,WAAU;kDACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;gDAEC,MAAM,QAAQ,IAAI;gDAClB,OAAO,QAAQ,KAAK;gDACpB,aAAa,QAAQ,WAAW;gDAChC,OAAO,QAAQ;+CAJV,QAAQ,KAAK;;;;;;;;;;;;;;;;0CAW1B,6LAAC;;kDACC,6LAAC,wIAAA,CAAA,UAAO;wCAAC,OAAO;wCAAG,WAAU;kDAAwC;;;;;;kDAGrE,6LAAC;wCAAI,WAAU;kDACZ,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,6LAAC;gDAEC,MAAM,SAAS,IAAI;gDACnB,OAAO,SAAS,KAAK;gDACrB,aAAa,SAAS,WAAW;gDACjC,OAAO,QAAQ,MAAM;+CAJhB,SAAS,KAAK;;;;;;;;;;;;;;;;;;;;;;kCAY7B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,kIAAA,CAAA,OAAI;4BAAC,SAAQ;4BAAQ,WAAU;4BAAwB,IAAI;sCAC1D,cAAA,6LAAC,kIAAA,CAAA,cAAW;;kDACV,6LAAC,wIAAA,CAAA,UAAO;wCAAC,OAAO;wCAAG,WAAU;kDAAwB;;;;;;kDAGrD,6LAAC,wIAAA,CAAA,OAAI;wCAAC,WAAU;kDAA0B;;;;;;kDAI1C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAO,WAAU;0DAA+I;;;;;;0DAGjK,6LAAC;gDAAO,WAAU;0DAA6J;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU/L;IAhNM;MAAA;uCAkNS", "debugId": null}}]}