'use client';

import { forwardRef } from 'react';
import { clsx } from 'clsx';

const Container = forwardRef(({ 
  children, 
  className, 
  size = 'default',
  center = true,
  ...props 
}, ref) => {
  const baseClasses = 'w-full px-4 sm:px-6 lg:px-8';
  
  const sizes = {
    sm: 'max-w-3xl',
    default: 'max-w-7xl',
    lg: 'max-w-screen-2xl',
    full: 'max-w-none',
  };
  
  const centerClass = center ? 'mx-auto' : '';
  
  const classes = clsx(
    baseClasses,
    sizes[size],
    centerClass,
    className
  );
  
  return (
    <div
      ref={ref}
      className={classes}
      {...props}
    >
      {children}
    </div>
  );
});

Container.displayName = 'Container';

export default Container;
