'use client';

import { forwardRef } from 'react';
import { clsx } from 'clsx';

const Card = forwardRef(({ 
  children, 
  className, 
  variant = 'default',
  hover = true,
  glow = false,
  tilt = false,
  ...props 
}, ref) => {
  const baseClasses = 'rounded-xl border backdrop-blur-sm transition-all duration-300';
  
  const variants = {
    default: 'bg-neutral-dark/80 border-neutral-gray/30 text-neutral-white',
    glass: 'glass border-white/20 text-neutral-white',
    primary: 'bg-primary-blue/20 border-primary-cyan/50 text-neutral-white',
    accent: 'bg-accent-neon/10 border-accent-neon/30 text-neutral-white',
    dark: 'bg-neutral-black/90 border-neutral-dark text-neutral-white',
  };
  
  const hoverEffects = hover ? 'hover:scale-105 hover:shadow-lg' : '';
  const glowEffect = glow ? 'glow-hover' : '';
  const tiltEffect = tilt ? 'tilt-3d' : '';
  
  const classes = clsx(
    baseClasses,
    variants[variant],
    hoverEffects,
    glowEffect,
    tiltEffect,
    className
  );
  
  return (
    <div
      ref={ref}
      className={classes}
      {...props}
    >
      {children}
    </div>
  );
});

Card.displayName = 'Card';

const CardHeader = forwardRef(({ children, className, ...props }, ref) => (
  <div
    ref={ref}
    className={clsx('p-6 pb-0', className)}
    {...props}
  >
    {children}
  </div>
));

CardHeader.displayName = 'CardHeader';

const CardContent = forwardRef(({ children, className, ...props }, ref) => (
  <div
    ref={ref}
    className={clsx('p-6', className)}
    {...props}
  >
    {children}
  </div>
));

CardContent.displayName = 'CardContent';

const CardFooter = forwardRef(({ children, className, ...props }, ref) => (
  <div
    ref={ref}
    className={clsx('p-6 pt-0', className)}
    {...props}
  >
    {children}
  </div>
));

CardFooter.displayName = 'CardFooter';

const CardTitle = forwardRef(({ children, className, ...props }, ref) => (
  <h3
    ref={ref}
    className={clsx('text-xl font-heading font-semibold leading-none tracking-tight', className)}
    {...props}
  >
    {children}
  </h3>
));

CardTitle.displayName = 'CardTitle';

const CardDescription = forwardRef(({ children, className, ...props }, ref) => (
  <p
    ref={ref}
    className={clsx('text-sm text-neutral-gray leading-relaxed', className)}
    {...props}
  >
    {children}
  </p>
));

CardDescription.displayName = 'CardDescription';

export { Card, CardHeader, CardContent, CardFooter, CardTitle, CardDescription };
