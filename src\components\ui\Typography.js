'use client';

import { forwardRef } from 'react';
import { clsx } from 'clsx';

const Heading = forwardRef(({ 
  children, 
  className, 
  level = 1,
  variant = 'default',
  gradient = false,
  neon = false,
  typewriter = false,
  ...props 
}, ref) => {
  const Tag = `h${level}`;
  
  const baseClasses = 'font-heading font-bold leading-tight tracking-tight';
  
  const sizes = {
    1: 'text-4xl sm:text-5xl lg:text-6xl xl:text-7xl',
    2: 'text-3xl sm:text-4xl lg:text-5xl xl:text-6xl',
    3: 'text-2xl sm:text-3xl lg:text-4xl xl:text-5xl',
    4: 'text-xl sm:text-2xl lg:text-3xl xl:text-4xl',
    5: 'text-lg sm:text-xl lg:text-2xl xl:text-3xl',
    6: 'text-base sm:text-lg lg:text-xl xl:text-2xl',
  };
  
  const variants = {
    default: 'text-neutral-white',
    primary: 'text-primary-cyan',
    accent: 'text-accent-neon',
    gradient: 'bg-gradient-to-r from-primary-cyan via-primary-blue to-primary-purple bg-clip-text text-transparent',
  };
  
  const effects = {
    neon: neon ? 'neon-text' : '',
    typewriter: typewriter ? 'typewriter' : '',
    gradient: gradient ? variants.gradient : variants[variant],
  };
  
  const classes = clsx(
    baseClasses,
    sizes[level],
    gradient ? effects.gradient : variants[variant],
    effects.neon,
    effects.typewriter,
    className
  );
  
  return (
    <Tag
      ref={ref}
      className={classes}
      {...props}
    >
      {children}
    </Tag>
  );
});

Heading.displayName = 'Heading';

const Text = forwardRef(({ 
  children, 
  className, 
  size = 'base',
  variant = 'default',
  weight = 'normal',
  ...props 
}, ref) => {
  const baseClasses = 'font-body leading-relaxed';
  
  const sizes = {
    xs: 'text-xs',
    sm: 'text-sm',
    base: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl',
    '2xl': 'text-2xl',
  };
  
  const variants = {
    default: 'text-neutral-white',
    muted: 'text-neutral-gray',
    primary: 'text-primary-cyan',
    accent: 'text-accent-neon',
    success: 'text-secondary-green',
    warning: 'text-secondary-orange',
    danger: 'text-secondary-pink',
  };
  
  const weights = {
    light: 'font-light',
    normal: 'font-normal',
    medium: 'font-medium',
    semibold: 'font-semibold',
    bold: 'font-bold',
  };
  
  const classes = clsx(
    baseClasses,
    sizes[size],
    variants[variant],
    weights[weight],
    className
  );
  
  return (
    <p
      ref={ref}
      className={classes}
      {...props}
    >
      {children}
    </p>
  );
});

Text.displayName = 'Text';

const Code = forwardRef(({ 
  children, 
  className, 
  inline = true,
  ...props 
}, ref) => {
  const baseClasses = 'font-mono bg-neutral-dark/50 border border-neutral-gray/30 text-accent-neon';
  
  const inlineClasses = inline 
    ? 'px-1.5 py-0.5 rounded text-sm' 
    : 'block p-4 rounded-lg text-sm overflow-x-auto';
  
  const classes = clsx(
    baseClasses,
    inlineClasses,
    className
  );
  
  const Tag = inline ? 'code' : 'pre';
  
  return (
    <Tag
      ref={ref}
      className={classes}
      {...props}
    >
      {inline ? children : <code>{children}</code>}
    </Tag>
  );
});

Code.displayName = 'Code';

const Link = forwardRef(({ 
  children, 
  className, 
  variant = 'default',
  external = false,
  ...props 
}, ref) => {
  const baseClasses = 'transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2';
  
  const variants = {
    default: 'text-primary-cyan hover:text-primary-blue underline-offset-4 hover:underline focus:ring-primary-cyan',
    button: 'text-neutral-white hover:text-primary-cyan no-underline focus:ring-primary-cyan',
    muted: 'text-neutral-gray hover:text-neutral-white underline-offset-4 hover:underline focus:ring-neutral-gray',
  };
  
  const classes = clsx(
    baseClasses,
    variants[variant],
    className
  );
  
  const externalProps = external ? {
    target: '_blank',
    rel: 'noopener noreferrer'
  } : {};
  
  return (
    <a
      ref={ref}
      className={classes}
      {...externalProps}
      {...props}
    >
      {children}
      {external && (
        <span className="ml-1 text-xs">↗</span>
      )}
    </a>
  );
});

Link.displayName = 'Link';

export { Heading, Text, Code, Link };
