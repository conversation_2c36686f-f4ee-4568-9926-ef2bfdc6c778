'use client';

import { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import Container from '@/components/ui/Container';
import { Card, CardContent } from '@/components/ui/Card';
import { Heading, Text } from '@/components/ui/Typography';
import { 
  ExclamationTriangleIcon, 
  ClockIcon, 
  QuestionMarkCircleIcon,
  CheckCircleIcon,
  BoltIcon,
  ShieldCheckIcon 
} from '@heroicons/react/24/outline';

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

const StatCard = ({ icon: Icon, number, label, description, delay = 0 }) => {
  const [count, setCount] = useState(0);
  const cardRef = useRef(null);

  useEffect(() => {
    const element = cardRef.current;
    if (!element) return;

    const ctx = gsap.context(() => {
      // Animate the card entrance
      gsap.fromTo(element, 
        { 
          opacity: 0, 
          y: 50,
          scale: 0.8
        },
        {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 0.8,
          delay: delay,
          ease: "back.out(1.7)",
          scrollTrigger: {
            trigger: element,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse"
          }
        }
      );

      // Animate the number counting
      gsap.to({ value: 0 }, {
        value: parseInt(number),
        duration: 2,
        delay: delay + 0.5,
        ease: "power2.out",
        onUpdate: function() {
          setCount(Math.floor(this.targets()[0].value));
        },
        scrollTrigger: {
          trigger: element,
          start: "top 80%",
          toggleActions: "play none none reverse"
        }
      });
    }, element);

    return () => ctx.revert();
  }, [number, delay]);

  return (
    <Card 
      ref={cardRef}
      variant="glass" 
      className="text-center p-6 hover:scale-105 transition-transform duration-300"
      glow
    >
      <CardContent className="space-y-4">
        <div className="flex justify-center">
          <div className="w-16 h-16 bg-gradient-to-br from-primary-cyan to-primary-blue rounded-full flex items-center justify-center">
            <Icon className="w-8 h-8 text-white" />
          </div>
        </div>
        <div className="text-4xl font-bold text-primary-cyan">
          {count}{number.includes('%') ? '%' : number.includes('M') ? 'M+' : ''}
        </div>
        <Heading level={4} className="text-white">
          {label}
        </Heading>
        <Text variant="muted" size="sm">
          {description}
        </Text>
      </CardContent>
    </Card>
  );
};

const ProblemCard = ({ icon: Icon, title, description, delay = 0 }) => {
  const cardRef = useRef(null);

  useEffect(() => {
    const element = cardRef.current;
    if (!element) return;

    const ctx = gsap.context(() => {
      gsap.fromTo(element,
        { 
          opacity: 0, 
          x: -100,
          rotationY: -15
        },
        {
          opacity: 1,
          x: 0,
          rotationY: 0,
          duration: 1,
          delay: delay,
          ease: "power3.out",
          scrollTrigger: {
            trigger: element,
            start: "top 85%",
            end: "bottom 15%",
            toggleActions: "play none none reverse"
          }
        }
      );
    }, element);

    return () => ctx.revert();
  }, [delay]);

  return (
    <Card 
      ref={cardRef}
      variant="dark" 
      className="p-6 border-l-4 border-secondary-pink hover:border-secondary-orange transition-colors duration-300"
    >
      <CardContent className="flex items-start space-x-4">
        <div className="flex-shrink-0">
          <div className="w-12 h-12 bg-secondary-pink/20 rounded-lg flex items-center justify-center">
            <Icon className="w-6 h-6 text-secondary-pink" />
          </div>
        </div>
        <div className="flex-1">
          <Heading level={4} className="text-white mb-2">
            {title}
          </Heading>
          <Text variant="muted">
            {description}
          </Text>
        </div>
      </CardContent>
    </Card>
  );
};

const SolutionCard = ({ icon: Icon, title, description, delay = 0 }) => {
  const cardRef = useRef(null);

  useEffect(() => {
    const element = cardRef.current;
    if (!element) return;

    const ctx = gsap.context(() => {
      gsap.fromTo(element,
        { 
          opacity: 0, 
          x: 100,
          rotationY: 15
        },
        {
          opacity: 1,
          x: 0,
          rotationY: 0,
          duration: 1,
          delay: delay,
          ease: "power3.out",
          scrollTrigger: {
            trigger: element,
            start: "top 85%",
            end: "bottom 15%",
            toggleActions: "play none none reverse"
          }
        }
      );
    }, element);

    return () => ctx.revert();
  }, [delay]);

  return (
    <Card 
      ref={cardRef}
      variant="primary" 
      className="p-6 border-l-4 border-secondary-green hover:border-accent-neon transition-colors duration-300"
      glow
    >
      <CardContent className="flex items-start space-x-4">
        <div className="flex-shrink-0">
          <div className="w-12 h-12 bg-secondary-green/20 rounded-lg flex items-center justify-center">
            <Icon className="w-6 h-6 text-secondary-green" />
          </div>
        </div>
        <div className="flex-1">
          <Heading level={4} className="text-white mb-2">
            {title}
          </Heading>
          <Text variant="muted">
            {description}
          </Text>
        </div>
      </CardContent>
    </Card>
  );
};

const ProblemSolutionSection = () => {
  const sectionRef = useRef(null);
  const titleRef = useRef(null);

  useEffect(() => {
    const section = sectionRef.current;
    const title = titleRef.current;
    if (!section || !title) return;

    const ctx = gsap.context(() => {
      // Parallax effect for the section
      gsap.to(section, {
        yPercent: -50,
        ease: "none",
        scrollTrigger: {
          trigger: section,
          start: "top bottom",
          end: "bottom top",
          scrub: true
        }
      });

      // Title animation
      gsap.fromTo(title,
        { 
          opacity: 0, 
          y: 100,
          scale: 0.8
        },
        {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 1.2,
          ease: "power3.out",
          scrollTrigger: {
            trigger: title,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse"
          }
        }
      );
    }, section);

    return () => ctx.revert();
  }, []);

  const problems = [
    {
      icon: ExclamationTriangleIcon,
      title: "Complex Reporting Processes",
      description: "Citizens struggle with navigating multiple platforms and departments, leading to confusion and delayed responses to critical incidents."
    },
    {
      icon: ClockIcon,
      title: "Time-Consuming Procedures",
      description: "Current systems require extensive paperwork and multiple visits, discouraging people from reporting important offenses."
    },
    {
      icon: QuestionMarkCircleIcon,
      title: "Authority Confusion",
      description: "Uncertainty about which department handles specific types of offenses creates barriers to effective reporting."
    }
  ];

  const solutions = [
    {
      icon: CheckCircleIcon,
      title: "One-Click Reporting",
      description: "Submit any offense report with just a few taps, including photos, videos, and location data automatically."
    },
    {
      icon: BoltIcon,
      title: "AI-Powered Routing",
      description: "Smart categorization instantly directs reports to the correct authorities in Malaysia or Singapore."
    },
    {
      icon: ShieldCheckIcon,
      title: "Real-Time Tracking",
      description: "Get live updates on your report status and see exactly what actions are being taken by authorities."
    }
  ];

  const stats = [
    {
      icon: ClockIcon,
      number: "73",
      label: "Average Report Time",
      description: "Minutes wasted on current systems"
    },
    {
      icon: ExclamationTriangleIcon,
      number: "45",
      label: "Unreported Incidents",
      description: "Percentage due to complexity"
    },
    {
      icon: QuestionMarkCircleIcon,
      number: "67",
      label: "Wrong Department",
      description: "Reports sent to incorrect authorities"
    }
  ];

  return (
    <section ref={sectionRef} className="py-24 bg-gradient-to-b from-neutral-black to-neutral-dark relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-primary-cyan rounded-full filter blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-primary-purple rounded-full filter blur-3xl"></div>
      </div>

      <Container className="relative z-10">
        {/* Section Title */}
        <div ref={titleRef} className="text-center mb-20">
          <Heading level={2} className="mb-6" gradient>
            The Problem We're Solving
          </Heading>
          <Text size="xl" className="max-w-3xl mx-auto text-neutral-light">
            Current offense reporting systems are broken. We're building the future of civic engagement 
            with AI-powered, cross-border solutions that actually work.
          </Text>
        </div>

        {/* Current Problems Stats */}
        <div className="mb-20">
          <Heading level={3} className="text-center mb-12 text-secondary-pink">
            Current System Problems
          </Heading>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            {stats.map((stat, index) => (
              <StatCard
                key={stat.label}
                icon={stat.icon}
                number={stat.number}
                label={stat.label}
                description={stat.description}
                delay={index * 0.2}
              />
            ))}
          </div>
        </div>

        {/* Problems vs Solutions */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 mb-20">
          {/* Problems Column */}
          <div>
            <Heading level={3} className="text-center mb-8 text-secondary-pink">
              Current Problems
            </Heading>
            <div className="space-y-6">
              {problems.map((problem, index) => (
                <ProblemCard
                  key={problem.title}
                  icon={problem.icon}
                  title={problem.title}
                  description={problem.description}
                  delay={index * 0.3}
                />
              ))}
            </div>
          </div>

          {/* Solutions Column */}
          <div>
            <Heading level={3} className="text-center mb-8 text-secondary-green">
              Our Solutions
            </Heading>
            <div className="space-y-6">
              {solutions.map((solution, index) => (
                <SolutionCard
                  key={solution.title}
                  icon={solution.icon}
                  title={solution.title}
                  description={solution.description}
                  delay={index * 0.3 + 0.5}
                />
              ))}
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <Card variant="glass" className="max-w-2xl mx-auto p-8" glow>
            <CardContent>
              <Heading level={3} className="mb-4 text-accent-neon">
                Ready to Experience the Difference?
              </Heading>
              <Text className="mb-6 text-neutral-light">
                See how ReportU transforms offense reporting from a 73-minute ordeal 
                into a 30-second solution with real results.
              </Text>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="px-8 py-3 bg-primary-blue hover:bg-primary-cyan text-white rounded-lg font-medium transition-all duration-300 transform hover:scale-105 glow">
                  Try Interactive Demo
                </button>
                <button className="px-8 py-3 border-2 border-primary-cyan text-primary-cyan hover:bg-primary-cyan hover:text-neutral-black rounded-lg font-medium transition-all duration-300">
                  View Case Studies
                </button>
              </div>
            </CardContent>
          </Card>
        </div>
      </Container>
    </section>
  );
};

export default ProblemSolutionSection;
