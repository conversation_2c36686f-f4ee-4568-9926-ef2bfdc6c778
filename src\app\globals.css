@import "tailwindcss";

:root {
  --background: #000000;
  --foreground: #ffffff;

  /* Custom CSS Variables for ReportU */
  --primary-blue: #0066FF;
  --primary-cyan: #00FFFF;
  --primary-purple: #6600FF;
  --secondary-green: #00FF66;
  --secondary-orange: #FF6600;
  --secondary-pink: #FF0066;
  --neutral-black: #000000;
  --neutral-dark: #1A1A1A;
  --neutral-gray: #666666;
  --neutral-light: #F5F5F5;
  --neutral-white: #FFFFFF;
  --accent-gold: #FFD700;
  --accent-silver: #C0C0C0;
  --accent-neon: #39FF14;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-heading: var(--font-orbitron);
  --font-body: var(--font-inter);
  --font-mono: var(--font-fira-code);
}

/* Global Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-inter), sans-serif;
  line-height: 1.6;
  overflow-x: hidden;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--neutral-dark);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-cyan);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-blue);
}

/* Selection Styles */
::selection {
  background: var(--primary-cyan);
  color: var(--neutral-black);
}

::-moz-selection {
  background: var(--primary-cyan);
  color: var(--neutral-black);
}

/* Focus Styles */
:focus {
  outline: 2px solid var(--primary-cyan);
  outline-offset: 2px;
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Matrix Rain Effect */
.matrix-rain {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
  opacity: 0.1;
}

/* Glow Effects */
.glow {
  box-shadow: 0 0 20px var(--primary-cyan);
}

.glow-hover:hover {
  box-shadow: 0 0 30px var(--primary-cyan);
  transition: box-shadow 0.3s ease;
}

/* Neon Text Effect */
.neon-text {
  color: var(--primary-cyan);
  text-shadow:
    0 0 5px var(--primary-cyan),
    0 0 10px var(--primary-cyan),
    0 0 15px var(--primary-cyan),
    0 0 20px var(--primary-cyan);
}

/* Cyber Grid Background */
.cyber-grid {
  background-image:
    linear-gradient(rgba(0,255,255,0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0,255,255,0.1) 1px, transparent 1px);
  background-size: 50px 50px;
}

/* Floating Animation */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

.float {
  animation: float 6s ease-in-out infinite;
}

/* Pulse Glow Animation */
@keyframes pulse-glow {
  0% { box-shadow: 0 0 5px rgba(0, 255, 255, 0.5); }
  100% { box-shadow: 0 0 20px rgba(0, 255, 255, 1); }
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite alternate;
}

/* Typewriter Effect */
.typewriter {
  overflow: hidden;
  border-right: 2px solid var(--primary-cyan);
  white-space: nowrap;
  animation:
    typewriter 3s steps(40) 1s 1 normal both,
    blink 1s infinite;
}

@keyframes typewriter {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blink {
  0%, 50% { border-color: transparent; }
  51%, 100% { border-color: var(--primary-cyan); }
}

/* 3D Tilt Effect */
.tilt-3d {
  transform-style: preserve-3d;
  transition: transform 0.3s ease;
}

.tilt-3d:hover {
  transform: perspective(1000px) rotateX(10deg) rotateY(10deg);
}

/* Glass Morphism */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Button Hover Effects */
.btn-hover {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.btn-hover::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn-hover:hover::before {
  left: 100%;
}

/* Loading Spinner */
.spinner {
  border: 2px solid rgba(0, 255, 255, 0.3);
  border-top: 2px solid var(--primary-cyan);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
