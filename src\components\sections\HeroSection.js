'use client';

import { useEffect, useRef, useState } from 'react';
import Container from '@/components/ui/Container';
import Button from '@/components/ui/Button';
import { Heading, Text } from '@/components/ui/Typography';
import { PlayIcon, ChevronDownIcon } from '@heroicons/react/24/outline';

const MatrixRain = () => {
  const canvasRef = useRef(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;

    const matrix = "REPORTU01MALAYSIA10SINGAPORE01ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789@#$%^&*()";
    const matrixArray = matrix.split("");

    const fontSize = 10;
    const columns = canvas.width / fontSize;
    const drops = [];

    for (let x = 0; x < columns; x++) {
      drops[x] = 1;
    }

    const draw = () => {
      ctx.fillStyle = 'rgba(0, 0, 0, 0.04)';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      ctx.fillStyle = '#00FFFF';
      ctx.font = fontSize + 'px monospace';

      for (let i = 0; i < drops.length; i++) {
        const text = matrixArray[Math.floor(Math.random() * matrixArray.length)];
        ctx.fillText(text, i * fontSize, drops[i] * fontSize);

        if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {
          drops[i] = 0;
        }
        drops[i]++;
      }
    };

    const interval = setInterval(draw, 35);

    const handleResize = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    window.addEventListener('resize', handleResize);

    return () => {
      clearInterval(interval);
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return (
    <canvas
      ref={canvasRef}
      className="absolute inset-0 opacity-10 pointer-events-none"
      style={{ zIndex: 1 }}
    />
  );
};

const TypewriterText = ({ text, delay = 100, className = "" }) => {
  const [displayText, setDisplayText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    if (currentIndex < text.length) {
      const timeout = setTimeout(() => {
        setDisplayText(prev => prev + text[currentIndex]);
        setCurrentIndex(prev => prev + 1);
      }, delay);

      return () => clearTimeout(timeout);
    }
  }, [currentIndex, text, delay]);

  return (
    <span className={className}>
      {displayText}
      <span className="animate-pulse">|</span>
    </span>
  );
};

const MiniDemo = () => {
  const [step, setStep] = useState(0);
  const steps = [
    { icon: '📱', text: 'Report Submitted', color: 'text-secondary-green' },
    { icon: '🤖', text: 'AI Processing...', color: 'text-primary-cyan' },
    { icon: '🏛️', text: 'Routed to Authority', color: 'text-accent-gold' },
    { icon: '✅', text: 'Case Opened', color: 'text-secondary-green' },
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setStep(prev => (prev + 1) % steps.length);
    }, 2000);

    return () => clearInterval(interval);
  }, [steps.length]);

  return (
    <div className="bg-neutral-dark/50 backdrop-blur-sm border border-primary-cyan/30 rounded-xl p-6 max-w-sm">
      <div className="text-center">
        <div className="text-4xl mb-2 animate-bounce">
          {steps[step].icon}
        </div>
        <Text className={`font-medium ${steps[step].color}`}>
          {steps[step].text}
        </Text>
        <div className="flex justify-center mt-3 space-x-1">
          {steps.map((_, index) => (
            <div
              key={index}
              className={`w-2 h-2 rounded-full transition-all duration-300 ${
                index === step ? 'bg-primary-cyan' : 'bg-neutral-gray'
              }`}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

const HeroSection = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const scrollToNext = () => {
    window.scrollTo({
      top: window.innerHeight,
      behavior: 'smooth'
    });
  };

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Matrix Rain Background */}
      <MatrixRain />
      
      {/* Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-neutral-black/80 via-transparent to-primary-blue/20" style={{ zIndex: 2 }} />
      
      {/* Cyber Grid */}
      <div className="absolute inset-0 cyber-grid opacity-20" style={{ zIndex: 3 }} />

      <Container className="relative z-10 text-center">
        <div className={`transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          {/* Main Headline */}
          <div className="mb-8">
            <Heading level={1} className="mb-4">
              <TypewriterText 
                text="Cross-Border Offense Reporting"
                delay={80}
                className="bg-gradient-to-r from-primary-cyan via-primary-blue to-primary-purple bg-clip-text text-transparent"
              />
            </Heading>
            <Heading level={2} className="text-neutral-white/90 font-normal">
              Revolutionizing Public Safety in{' '}
              <span className="text-secondary-green font-bold">Malaysia</span>
              {' & '}
              <span className="text-secondary-pink font-bold">Singapore</span>
            </Heading>
          </div>

          {/* Subtitle */}
          <div className="mb-12 max-w-3xl mx-auto">
            <Text size="xl" className="text-neutral-light leading-relaxed">
              AI-powered platform that instantly routes your offense reports to the right authorities. 
              Submit evidence, track progress, and make your community safer with just a few taps.
            </Text>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
            <Button 
              variant="primary" 
              size="xl" 
              className="group glow transform hover:scale-105 transition-all duration-300"
            >
              <PlayIcon className="w-6 h-6 mr-2 group-hover:animate-pulse" />
              Try Live Demo
            </Button>
            <Button 
              variant="secondary" 
              size="xl"
              className="group transform hover:scale-105 transition-all duration-300"
            >
              Watch Pitch Deck
              <ChevronDownIcon className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
            </Button>
          </div>

          {/* Stats Row */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            <div className="text-center">
              <div className="text-4xl font-bold text-primary-cyan mb-2">39M+</div>
              <Text variant="muted">Potential Users</Text>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-accent-neon mb-2">&lt;30s</div>
              <Text variant="muted">Report Submission</Text>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-secondary-green mb-2">24/7</div>
              <Text variant="muted">AI-Powered Routing</Text>
            </div>
          </div>

          {/* Mini Demo */}
          <div className="flex justify-center mb-16">
            <MiniDemo />
          </div>

          {/* Trust Indicators */}
          <div className="flex flex-wrap justify-center items-center gap-8 opacity-60">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-secondary-green rounded-full flex items-center justify-center">
                <span className="text-xs font-bold text-neutral-black">🔒</span>
              </div>
              <Text size="sm" variant="muted">End-to-End Encrypted</Text>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-primary-cyan rounded-full flex items-center justify-center">
                <span className="text-xs font-bold text-neutral-black">⚡</span>
              </div>
              <Text size="sm" variant="muted">Real-time Processing</Text>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-accent-gold rounded-full flex items-center justify-center">
                <span className="text-xs font-bold text-neutral-black">🏛️</span>
              </div>
              <Text size="sm" variant="muted">Government Ready</Text>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <button
          onClick={scrollToNext}
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-primary-cyan hover:text-primary-blue transition-colors duration-300 animate-bounce"
          aria-label="Scroll to next section"
        >
          <ChevronDownIcon className="w-8 h-8" />
        </button>
      </Container>
    </section>
  );
};

export default HeroSection;
