import { Inter, Orbitron, Fira_Code } from "next/font/google";
import "./globals.css";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: "swap",
});

const orbitron = Orbitron({
  variable: "--font-orbitron",
  subsets: ["latin"],
  display: "swap",
});

const firaCode = Fira_Code({
  variable: "--font-fira-code",
  subsets: ["latin"],
  display: "swap",
});

export const metadata = {
  title: "ReportU - Cross-Border Offense Reporting Platform",
  description: "Revolutionary platform for Malaysia and Singapore citizens to efficiently submit offense reports with AI-powered routing to appropriate authorities.",
  keywords: "offense reporting, Malaysia, Singapore, cross-border, AI routing, civic engagement, government services",
  authors: [{ name: "ReportU Team" }],
  creator: "ReportU",
  publisher: "ReportU",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://reportu.app'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: "ReportU - Cross-Border Offense Reporting Platform",
    description: "Revolutionary platform for Malaysia and Singapore citizens to efficiently submit offense reports with AI-powered routing to appropriate authorities.",
    url: 'https://reportu.app',
    siteName: 'ReportU',
    images: [
      {
        url: 'https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp',
        width: 1200,
        height: 630,
        alt: 'ReportU Logo',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "ReportU - Cross-Border Offense Reporting Platform",
    description: "Revolutionary platform for Malaysia and Singapore citizens to efficiently submit offense reports with AI-powered routing to appropriate authorities.",
    images: ['https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  icons: {
    icon: 'https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp',
    shortcut: 'https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp',
    apple: 'https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp',
  },
};

export default function RootLayout({ children }) {
  return (
    <html lang="en" className="scroll-smooth">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      </head>
      <body
        className={`${inter.variable} ${orbitron.variable} ${firaCode.variable} font-body antialiased bg-neutral-black text-neutral-white overflow-x-hidden`}
      >
        <div className="min-h-screen bg-gradient-to-br from-neutral-black via-neutral-dark to-primary-blue/10">
          {children}
        </div>
      </body>
    </html>
  );
}
