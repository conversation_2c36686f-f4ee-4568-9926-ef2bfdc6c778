'use client';

import { forwardRef } from 'react';
import { clsx } from 'clsx';

const Button = forwardRef(({ 
  children, 
  className, 
  variant = 'primary', 
  size = 'md', 
  disabled = false,
  loading = false,
  onClick,
  type = 'button',
  ...props 
}, ref) => {
  const baseClasses = 'inline-flex items-center justify-center font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed btn-hover';
  
  const variants = {
    primary: 'bg-primary-blue hover:bg-primary-cyan text-white focus:ring-primary-cyan glow-hover',
    secondary: 'bg-transparent border-2 border-primary-cyan text-primary-cyan hover:bg-primary-cyan hover:text-neutral-black focus:ring-primary-cyan',
    accent: 'bg-accent-neon hover:bg-accent-gold text-neutral-black focus:ring-accent-neon',
    ghost: 'bg-transparent text-primary-cyan hover:bg-primary-cyan/10 focus:ring-primary-cyan',
    danger: 'bg-secondary-pink hover:bg-secondary-orange text-white focus:ring-secondary-pink',
  };
  
  const sizes = {
    sm: 'px-3 py-1.5 text-sm rounded-md',
    md: 'px-4 py-2 text-base rounded-lg',
    lg: 'px-6 py-3 text-lg rounded-xl',
    xl: 'px-8 py-4 text-xl rounded-2xl',
  };
  
  const classes = clsx(
    baseClasses,
    variants[variant],
    sizes[size],
    className
  );
  
  return (
    <button
      ref={ref}
      type={type}
      className={classes}
      disabled={disabled || loading}
      onClick={onClick}
      {...props}
    >
      {loading && (
        <div className="w-4 h-4 mr-2 spinner"></div>
      )}
      {children}
    </button>
  );
});

Button.displayName = 'Button';

export default Button;
